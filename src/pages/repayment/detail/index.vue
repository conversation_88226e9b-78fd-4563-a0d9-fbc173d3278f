<script setup>
import { onServerPrefetch, computed, onMounted, ref } from "vue";
import useStore from "./store";
import { initStore, request, handleHtmlText } from "../../../helpers/utils";

import { back, regNativeEvent, report } from "../../../helpers/native-bridge";
import ContractDetailPopup from "../../../components/ContractDetailPopup.vue";
import PdfPreview from "../../../components/pdfPreview.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

// 1 pdf 2 html 默认pdf
const contracttype = ref(1);
function parseContractUrl(url) {
  try {
    const cUrl = new URL(url);
    if (!cUrl.pathname.endsWith("pdf")) {
      contracttype.value = 2;
    }
  } catch (e) {
    console.error(e);
  }
}

// 获取已签协议详情
async function getContractDetail(item) {
  const temp = item;
  // 度小满不返回contractUrl，京东白条返回contractUrl
  if (!temp.contractUrl) {
    temp.isLoading = true;
    const res = await request(
      "/loan/api/contract/detail",
      { type: 2, contractId: item.contractId },
      { mock: false },
    );
    temp.isLoading = false;
    if (res.code !== 0) {
      return;
    }
    data.value.contractHtml = handleHtmlText(res.data[0]?.contractContent);
    data.value.isShowContractDetailPopup = true;
    window.location.hash = "isContractDetailPopup";
  } else {
    parseContractUrl(temp.contractUrl);
    temp.isLoading = true;
    setTimeout(() => {
      temp.isLoading = false;
    }, 500);
    data.value.pdfKey += 1;
    data.value.contractUrl = item.contractUrl;
    data.value.isShowContractDetailPopupUrl = true;
    window.location.hash = "isContractDetailPopupUrl";
  }
}

regNativeEvent("onBack", () => {
  back();
});

function hideContractDetail() {
  data.value.isShowContractDetailPopup = false;
  data.value.isShowContractDetailPopupUrl = false;
  back();
}

// 还款计划默认滑到最新的待还款设置
const planCon = ref(null);
function setPlanConScrollTop() {
  let tempScrollTop = 0;
  for (let i = 1; i <= data.value.planConpleteIndex + 1; i += 1) {
    tempScrollTop += planCon.value.childNodes[i].offsetHeight;
  }
  planCon.value.scrollTop = tempScrollTop;
}
const startWeb = ref("hidden");
onMounted(async () => {
  setTimeout(() => {
    startWeb.value = "visible";
  });
  if (data.value.planConpleteIndex > -1) {
    setPlanConScrollTop();
  }
  window.addEventListener("hashchange", () => {
    if (window.location.hash === "#isContractPopUp") {
      data.value.isShowContract = true;
      data.value.isShowContractDetailPopup = false;
      data.value.isShowContractDetailPopupUrl = false;
    } else if (window.location.hash === "#isContractDetailPopup") {
      data.value.isShowContractDetailPopup = true;
      data.value.isShowContract = false;
      data.value.isShowContractDetailPopupUrl = false;
    } else if (window.location.hash === "#isContractDetailPopupUrl") {
      data.value.isShowContractDetailPopupUrl = true;
      data.value.isShowContract = false;
      data.value.isShowContractDetailPopup = false;
    } else {
      data.value.isShowContract = false;
    }
  });
  const res = await getCurrentCpInfo();
  // 服务提供商
  data.value.supplierName = res?.supplierName;
  report("wallet_page_view", {
    page_name: "repayment_detail_page",
    orderStatus: store.getOrderStatus(),
  });
});

function showContractPopup() {
  data.value.isShowContract = true;
  window.location.hash = "isContractPopUp";
}

function closeContractPopup() {
  data.value.isShowContract = false;
  back();
}

function showMore() {
  data.value.isShowMore = !data.value.isShowMore;
}

function addCommas(num) {
  return (num / 100)
    .toFixed(2)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 还款计划的二级标题
const planTitle = computed(() => {
  let tempTitle = "";
  if (data.value.isOverdue) {
    tempTitle = `逾期欠款￥${addCommas(data.value.overdueDebts)}，请尽快还款`;
  } else {
    tempTitle = `已还￥${addCommas(data.value.repaidAmount)}，待还总额￥${addCommas(data.value.waitRepayAmount)}`;
  }
  return tempTitle;
});

function filterDate(dateString, type) {
  const year = dateString?.substring(0, 4);
  const month = parseInt(dateString?.substring(4, 6), 10);
  const day = parseInt(dateString?.substring(6, 8), 10);
  if (type === "cn") {
    return `${year}年${month}月${day}日`;
  }
  return `${year}/${month}/${day}`;
}

const overdueDaysTip = `您已逾期${data.value.overdueDays}天，为保障您的良好信用记录，请按时还款。`;

const principalAndInterest = (termPrincipal, termInterest) =>
  `本金￥${addCommas(termPrincipal)} + 利息￥${addCommas(termInterest)}`;
</script>

<template>
  <div v-if="data.isLoading" class="loading-box">
    <hnr-loading />
  </div>
  <div v-else class="detail-container" :style="{ visibility: startWeb }">
    <hnr-nav-bar transparent="true" class="nav-padding-top">
      <template #left>
        <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
      </template>
      <template #title>
        <div style="display: flex; flex-direction: row; align-items: center; position: relative">
          <span class="white-text">借款详情</span>
        </div>
      </template>
    </hnr-nav-bar>
    <hnr-notify
      class="tip-box"
      type="warning"
      :text="overdueDaysTip"
      left-icon="/wallet-loan-web/pages/tip.svg"
      :is-show="data.isOverdue"
    />
    <main>
      <div class="detail-header-box">
        <div class="detail-header-con">
          <div class="detail-header-date">{{ filterDate(data.loanDate, "cn") }}借款</div>
          <div class="detail-header-money">￥{{ addCommas(data.loanPrincipal) }}</div>
          <div class="divider"></div>
          <div class="list-box">
            <div class="item">
              <div class="item-left">借款状态</div>
              <div v-if="data.status === 1" class="item-right">申请中</div>
              <div v-else-if="data.status === 2" class="item-right">未结清</div>
              <div v-else-if="data.status === 3" class="item-right item-right-overdue">
                已逾期 {{ data.overdueDays }} 天
              </div>
              <div v-else-if="data.status === 4" class="item-right">已结清</div>
            </div>
            <div class="item">
              <div class="item-left">应还总额</div>
              <div class="item-right">￥{{ addCommas(data.totalInstallmentAmount) }}</div>
            </div>
            <div class="item">
              <div class="item-left">借款本金</div>
              <div class="item-right">￥{{ addCommas(data.loanPrincipal) }}</div>
            </div>
            <div class="item">
              <div class="item-left">利息</div>
              <div class="item-right">￥{{ addCommas(data.installmentInterest) }}</div>
            </div>
            <div v-if="parseFloat(data?.serviceFee)" class="item">
              <div class="item-left">服务费</div>
              <div class="item-right">￥{{ addCommas(data?.serviceFee) }}</div>
            </div>
            <div v-if="data.prePenalty" class="item">
              <div class="item-left">违约金</div>
              <div class="item-right">￥{{ addCommas(data.prePenalty) }}</div>
            </div>
            <div v-if="data.lateCharge" class="item">
              <div class="item-left">逾期罚息</div>
              <div class="item-right">￥{{ addCommas(data.lateCharge) }}</div>
            </div>
            <!-- 度小满显示优惠，京东白条不显示优惠 -->
            <div v-if="data.interestDiscount" class="item">
              <div class="item-left">优惠金额</div>
              <div class="item-right">- ￥{{ addCommas(data.interestDiscount) }}</div>
            </div>
            <template v-if="data.isShowMore">
              <div class="divider divider2"></div>
              <div class="item">
                <div class="item-left">借款订单号</div>
                <div class="item-right light-color">{{ data.loanOrderNumber }}</div>
              </div>
              <div class="item">
                <div class="item-left">分期总期数</div>
                <div class="item-right light-color">{{ data.totalInstallments }}期</div>
              </div>
              <div class="item">
                <div class="item-left">还款方式</div>
                <div class="item-right light-color">{{ data.repayMethodName }}</div>
              </div>
              <div class="item">
                <div class="item-left">实际年利率</div>
                <div class="item-right light-color">{{ data.apr }}%</div>
              </div>
              <div v-if="data.dailyInterestRate" class="item">
                <div class="item-left">实际日利率</div>
                <div class="item-right light-color">{{ data.dailyInterestRate }}%</div>
              </div>
              <div class="item">
                <div class="item-left">服务提供商</div>
                <div class="item-right light-color">{{ data.supplierName }}</div>
              </div>
              <div v-if="data.contributor" class="item">
                <div class="item-left">出资方</div>
                <div class="item-right light-color">{{ data.contributor }}</div>
              </div>
              <div class="item">
                <div class="item-left">收款银行卡</div>
                <div class="item-right light-color">
                  {{ data.receivingBank }}({{ data.bindCardNo.slice(-4) }})
                </div>
              </div>
              <div class="item">
                <div class="item-left">合同及协议</div>
                <div class="item-right check-btn" @click="showContractPopup">查看</div>
              </div>
            </template>
            <div class="item">
              <div class="item-left"></div>
              <div v-if="!data.isShowMore" class="item-right light-color" @click="showMore">
                更多明细
                <span class="icon-arrow-down icon-arrow-up"></span>
              </div>
              <div v-else class="item-right light-color" @click="showMore">
                收起
                <span class="icon-arrow-down"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <hnr-nav-bar
        transparent="true"
        class="plan-title"
        :class="data.isOverdue ? 'plan-title-overdue' : ''"
        title="还款计划"
        :subtitle="planTitle"
      />
      <div class="plan-box">
        <div class="plan-con-box">
          <div ref="planCon" class="plan-con">
            <div v-for="item in data.repayPlanTerms" :key="item.outOrderNo" class="plan-item">
              <div class="plan-item-left">
                <div class="plan-item-left-top">
                  <span class="plan-item-left-top-termNo"
                    >{{ item.termNo }}/{{ data.totalInstallments }}期</span
                  >
                  <span v-if="item.termStatus === 1" class="plan-label plan-label-end">已还清</span>
                  <span v-if="item.termStatus === 2" class="plan-label plan-label-in-progress"
                    >进行中</span
                  >
                  <span v-if="item.termStatus === 3" class="plan-label plan-label-in-progress"
                    >进行中</span
                  >
                  <span v-if="item.termStatus === 4" class="plan-label plan-label-overdue"
                    >已逾期{{ item.overdueDays }}天</span
                  >
                  <span v-if="item.termStatus === 5" class="plan-label plan-label-in-progress"
                    >宽限期</span
                  >
                </div>
                <div class="plan-item-left-bottom">
                  还款日{{ filterDate(item.shouldRepayDate) }}
                </div>
              </div>
              <div class="plan-item-right">
                <div class="plan-item-right-top">
                  <div v-if="item.paidTermAmount">
                    <span
                      ><span v-if="item.termStatus !== 1">应还</span>￥{{
                        addCommas(item.termAmount - item.termReductionAmount)
                      }}</span
                    >
                    <br />
                    <span v-if="item.termStatus !== 1"
                      >已还￥{{
                        addCommas(item.paidTermAmount - item.paidTermReductionAmount)
                      }}</span
                    >
                  </div>
                  <div v-else>￥{{ addCommas(item.termAmount - item.termReductionAmount) }}</div>
                </div>
                <div class="plan-item-right-mid">
                  {{ principalAndInterest(item.termPrincipal, item.termInterest) }}
                </div>
                <div v-if="parseFloat(item?.termServiceFee)" class="plan-item-right-mid">
                  + 服务费￥{{ addCommas(item?.termServiceFee) }}
                </div>
                <div
                  v-if="
                    item.termPrinPenalty + item.termInterPenalty ||
                    item.termPenalty ||
                    item.paidTermViolateFee
                  "
                  class="plan-item-right-bottom"
                  :class="
                    item.overdue ? 'plan-item-right-bottom-overdue' : 'plan-item-right-bottom-hui'
                  "
                >
                  <span v-if="item.paidTermViolateFee"
                    >+ 违约金 ￥{{ addCommas(item.paidTermViolateFee) }}</span
                  >
                  <span v-if="item.termPrinPenalty + item.termInterPenalty || item.termPenalty">
                    + 罚息 ￥{{
                      addCommas(item.termPrinPenalty + item.termInterPenalty || item.termPenalty)
                    }}</span
                  >
                </div>
                <!-- 度小满显示优惠，京东白条不显示优惠 -->
                <div v-if="item.termReductionAmount" class="plan-item-right-bottom">
                  - 优惠 ￥{{ addCommas(item.termReductionAmount) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <no-ssr>
    <contract-detail-popup
      :is-show-contract-detail-popup="data.isShowContractDetailPopup"
      :contract-html="data.contractHtml"
      @close="hideContractDetail"
    />
    <contract-detail-popup
      :is-show-contract-detail-popup="data.isShowContractDetailPopupUrl && contracttype === 2"
      :contract-content="data.contractUrl"
      @close="hideContractDetail"
    />
    <pdf-preview
      :pdf-key="data.pdfKey"
      :is-show-contract-detail-popup="data.isShowContractDetailPopupUrl && contracttype === 1"
      :contract-url="data.contractUrl"
      :view-height="1000"
      :view-width="1000"
      @close="hideContractDetail"
    />
    <hnr-popup v-model:show="data.isShowContract" round position="bottom" class="contract-popup">
      <div class="pull-icon-box">
        <span class="icon-arrow-pull" teleport="hnr-divider" @click="closeContractPopup"></span>
      </div>
      <div class="contract-con">
        <hnr-sub-title :sticky="false" style="height: auto">
          <hnr-index-anchor nav-bar-height="112px" bottom-tab-height="20px" title="合同及协议" />
        </hnr-sub-title>
        <hnr-cell-group inset>
          <hnr-cell
            v-for="item in data.agreementList"
            :key="item.contractId"
            class="contract-item"
            :title="item.contractName"
            border
            @click="getContractDetail(item)"
          >
            <template #value>
              <hnr-loading
                v-show="item.isLoading"
                size="small"
                style="margin-right: var(--hnr-elements-margin-vertical-M)"
              />
              <span class="icon-arrow-right"></span>
            </template>
          </hnr-cell>
        </hnr-cell-group>
      </div>
    </hnr-popup>
  </no-ssr>
</template>

<style scoped>
.detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  user-select: none;
}

:deep(.hnr-nav-bar__title) {
  font-weight: var(--hnr-font-weight-medium);
}

main {
  flex: 1;
  overflow: auto;
}

.detail-header-box {
  padding: var(--hnr-elements-margin-vertical-M2);
  padding-bottom: 0;
}

.detail-header-con {
  padding: var(--hnr-elements-margin-vertical-L);
  background: var(--hnr-card-background);
  border-radius: var(--hnr-card-border-radius);
}

:deep(.hnr-card__content) {
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.detail-header-date {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  text-align: center;
}

.detail-header-money {
  margin-top: var(--hnr-elements-margin-vertical-S);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-6);
  font-weight: var(--hnr-font-weight-medium);
  text-align: center;
}

.divider {
  width: 100%;
  height: var(--dp1);
  background: #dfdfdf;
  margin-top: var(--hnr-elements-margin-vertical-M2);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}

.divider2 {
  margin-top: var(--hnr-elements-margin-vertical-L);
}

.list-box {
  width: 100%;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}

.item:last-child {
  margin-bottom: 0;
}

.item-left {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  align-items: center;
}

.item-right {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.light-color {
  color: var(--hnr-text-color-secondary);
}

.check-btn {
  color: var(--hnr-text-color-primary-activated);
  font-size: var(--hnr-button-2);
}

.icon-arrow-down:before {
  content: "\e901";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-color-tertiary);
  display: inline-block;
}

.icon-arrow-up:before {
  transform: scaleY(-1);
}

.plan-title {
  margin-left: 0;
  padding: 8px 0 3px;
}
.plan-title :deep(.hnr-nav-bar__title) {
  font-size: var(--hnr-subtitle-2) !important;
}

.plan-box {
  padding: 0 var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-horizontal-L);
  box-sizing: border-box;
}
.plan-con-box {
  padding-right: var(--dp5);
  background: var(--hnr-card-background);
  border-radius: var(--hnr-card-border-radius);
}
.plan-con {
  padding: 0 var(--hnr-elements-margin-vertical-L);
  padding-right: var(--dp8);
  max-height: calc(var(--dp100) + var(--dp200));
  overflow: auto;
}

.plan-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  border-bottom: var(--dp1) solid #e7e7e7;
  padding: var(--hnr-elements-margin-vertical-M2) 0;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-item-left {
  width: 105px;
  align-self: self-start;
  margin-right: var(--hnr-elements-margin-horizontal-L);
}

.plan-item-left-top {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weigth-medium);
  white-space: nowrap;
}

.plan-item-left-top-termNo {
  font-weight: var(--hnr-font-weight-medium);
}

.plan-item-left-bottom,
.plan-item-right-mid {
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.plan-item-left-bottom {
  margin-top: var(--dp2);
  white-space: nowrap;
}

.plan-item-right {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.plan-item-right-top {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weigth-regular);
}

.plan-item-right-bottom {
  color: #ff590c;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.plan-item-right-bottom-overdue {
  color: var(--hnr-color-error);
}
.plan-item-right-bottom-hui {
  color: var(--hnr-text-color-tertiary);
}

.contract-popup {
  min-height: calc(var(--dp100) + var(--dp260));
  max-height: calc(100vh - var(--dp60));
  display: flex;
  flex-direction: column;
  user-select: none;
}

.pull-icon-box {
  text-align: center;
}

.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp20);
  color: #d5d5d5;
}

.pull-icon {
  width: var(--dp32);
  height: var(--dp18);
}

:deep(.hnr-cell__right-icon--icon) {
  width: auto;
}

.contract-con {
  flex: 1;
  overflow: auto;
}

:deep(.hnr-index-anchor__left-text-title) {
  color: var(--hnr-text-color-primary);
  font-size: calc(var(--hnr-nav-bar-title-font-size) / var(--hnr-large-font-rate));
  font-weight: var(--hnr-font-weigth-medium);
}

.plan-label {
  border-radius: var(--hnr-text-click-effect-corner-radius);
  font-size: var(--hnr-caption);
  font-weight: var(--hnr-font-weight-regular);
  padding: var(--hnr-elements-margin-vertical-XS) var(--hnr-elements-margin-vertical-S);
  margin-left: var(--hnr-elements-margin-horizontal-S);
  transform: translateY(-2px);
  display: inline-block;
}

.plan-label-in-progress {
  background: #e3f1ff;
  color: var(--hnr-text-color-primary-activated);
}

.plan-label-end {
  background: var(--hnr-color-control-normal);
  color: var(--hnr-text-color-secondary);
}

.plan-label-overdue {
  background: #ffebee;
  color: var(--hnr-color-error);
}

.help-icon {
  width: var(--dp16);
  height: var(--dp16);
  display: inline-block;
  transform: translateY(var(--dp2));
  margin-left: var(--hnr-elements-margin-horizontal-XS);
}

:deep(.hnr-notify__centerMessage) {
  margin-right: var(--dp10);
}

:deep(.hnr-notify__left-icon img) {
  width: var(--dp24);
}

.plan-title-overdue :deep(.hnr-nav-bar__subtitle) {
  color: var(--hnr-color-error);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weigth-regular);
}

.tip-box :deep(.hnr-notify__left-icon) {
  color: transparent !important;
}

.contract-item :deep(.hnr-cell__right-icon--icon) {
  color: transparent !important;
}

.icon-arrow-right:before {
  content: "\e903";
  font-family: "icomoon";
  font-size: var(--dp18);
  color: var(--hnr-color-tertiary);
}
.plan-con::-webkit-scrollbar {
  width: var(--dp3);
  -ms-overflow-style: scrollbar;
  scrollbar-width: thin;
}

.plan-con::-webkit-scrollbar-thumb {
  border-radius: var(--dp3);
  background-color: #ccc;
}
.loading-box {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-right-overdue {
  color: var(--hnr-color-error);
}
:deep(.hnr-cell__left) {
  max-width: calc(100% - 57px) !important;
}
@media (prefers-color-scheme: dark) {
  :deep(.hnr-popup) {
    background: #2e2e2e;
  }
  .icon-arrow-pull:before {
    color: #ffffff33;
  }
  .contract-item {
    background: var(--hnr-color-card-background);
  }
}
:deep(.hnr-overlay) {
  outline: none;
}
</style>

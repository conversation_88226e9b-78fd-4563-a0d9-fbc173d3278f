<script setup>
import { onServerPrefetch, onMounted, ref } from "vue";
import useStore from "./store";
import { initStore } from "../../../helpers/utils";
import { back, report } from "../../../helpers/native-bridge";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

function goback() {
  back();
}

function filterYearMonth(dateString) {
  const year = dateString?.substring(0, 4);
  const month = parseInt(dateString?.substring(5, 7), 10);
  return `${year}年${month}月`;
}

function filterDate(dateString) {
  const month = parseInt(dateString?.substring(5, 7), 10);
  const day = parseInt(dateString?.substring(8, 10), 10);
  const hhmmss = dateString?.substring(11, 19);
  return `${month}/${day} ${hhmmss}`;
}

function addCommas(num) {
  return (num / 100)
    .toFixed(2)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
const startWeb = ref("hidden");
onMounted(() => {
  setTimeout(() => {
    startWeb.value = "visible";
  });
  report("wallet_page_view", {
    page_name: "repayment_record_page",
    pendingNum: store.getRepayStatusNum(1),
    successNum: store.getRepayStatusNum(2),
    partialSuccessNum: store.getRepayStatusNum(3),
    failedNum: store.getRepayStatusNum(4),
    totalNum: store.getTotalNum(),
  });
});
</script>

<template>
  <div class="list-container" :style="{ visibility: startWeb }">
    <hnr-nav-bar transparent="true" class="nav-padding-top" title="还款记录">
      <template #left>
        <icsvg-public-back-filled @click="goback"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <main v-if="data.recordArr?.length > 0">
      <div v-for="monthItem in data.recordArr" :key="monthItem.yearMonth" class="list-option">
        <div class="date-title">{{ filterYearMonth(monthItem.yearMonth) }}</div>
        <div class="item-box">
          <div class="item-box-con">
            <div v-for="(item, index) in monthItem.list" :key="item.outOrderNo" class="item">
              <div class="item-con">
                <div class="item-left">
                  <div class="item-left-top">
                    <div class="item-align">还款</div>
                    <div class="item-right-bottom">{{ filterDate(item.repayTime) }}</div>
                  </div>
                </div>
                <div class="item-right">
                  <div class="item-align item-right-top">￥{{ addCommas(item.repayAmount) }}</div>
                  <div v-if="item.repayStatus === 1" class="item-right-bottom">还款中</div>
                  <div v-else-if="item.repayStatus === 2" class="item-right-bottom">还款成功</div>
                  <div v-else-if="item.repayStatus === 3" class="item-right-bottom">
                    部分还款成功
                  </div>
                  <div v-else-if="item.repayStatus === 4" class="item-right-bottom fail-color">
                    还款失败
                  </div>
                </div>
                <div v-if="index !== monthItem.list.length - 1" class="divider"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div></div>
    </main>
    <main v-else class="no-record">
      <span class="icon-no-record"></span>
      <span class="no-record-tip">暂无还款记录</span>
    </main>
  </div>
</template>

<style scoped>
.list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: var(--hnr-elements-margin-vertical-L);
  box-sizing: border-box;
  user-select: none;
}
:deep(.hnr-nav-bar__title) {
  font-weight: var(--hnr-font-weight-medium);
}
main {
  flex: 1;
  overflow: auto;
}

.date-title {
  height: var(--dp56);
  margin-left: 0;
  padding: var(--hnr-elements-margin-vertical-L2) 29px 0;
  box-sizing: border-box;
  font-weight: var(--hnr-font-weight-medium);
  color: var(--hnr-text-color-secondary);
  font-size: var(--dp14);
}
.item-box {
  padding: 0 var(--hnr-elements-margin-vertical-M2);
}
.item-box-con {
  border-radius: var(--hnr-card-border-radius);
  background: var(--hnr-card-background);
}
.item {
  padding: var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-vertical-L);
  box-sizing: border-box;
  width: 100%;
}

.item-con {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.divider {
  width: 100%;
  height: var(--dp1);
  background: #dfdfdf;
  position: absolute;
  left: 0;
  bottom: -12px;
}
.item-left-top,
.item-right-top {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
}

.item-left-bottom,
.item-right-bottom {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: 3px;
}

.item-right {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.fail-color {
  color: #ff1d28;
}

.no-record {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: var(--hnr-elements-margin-vertical-XXL);
}
.icon-no-record:before {
  content: "\e949";
  font-family: "icomoon";
  font-size: var(--dp72);
  color: #c1c2c4;
}
.no-record-tip {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.item-align {
  display: flex;
  align-items: center;
}
</style>

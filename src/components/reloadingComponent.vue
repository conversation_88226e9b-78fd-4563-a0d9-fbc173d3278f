<template>
  <div v-show="props.loadingStatus" class="loadingStatusDiv">
    <div class="loading-content">
      <hnr-loading />
      <span class="loading-text">正在加载...</span>
    </div>
  </div>
  <div v-show="props.errorStatus" class="loadingStatusDiv">
    <div class="loading-content" @click="tryClick">
      <hnr-icon
        class="icon-notServe"
        name="/wallet-loan-web/pages/entrance/UniversalIcon_ServerError.svg"
      ></hnr-icon>
      <span class="loading-text">点击重试</span>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  loadingStatus: { type: Boolean, default: false },
  errorStatus: { type: Boolean, default: false },
});

const emit = defineEmits(["tryClick"]);
const tryClick = () => {
  emit("tryClick");
};
</script>

<style scoped>
.loadingStatusDiv {
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 100;
}
.loading-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.loading-text {
  color: var(--hnr-color-secondary);
  font-size: var(--hnr-body-2);
  margin-top: var(--hnr-elements-margin-vertical-M);
  font-weight: var(--hnr-font-weight-regular);
}
.icon-notServe {
  width: var(--dp72);
  height: auto;
  color: var(--hnr-color-control-normal-2);
}
:deep(.hnr-loading__spinner) {
  height: 72px;
  width: 72px;
}
:deep(.icon-notServe svg g g polygon) {
  fill: var(--hnr-text-color-primary);
}
</style>

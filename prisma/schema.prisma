generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model user {
  id       Int    @id @default(autoincrement())
  username String @unique
}

model scene {
  id           Int     @id @default(autoincrement())
  userId       Int?
  name         String?
  supplier     Int?    @map("supplier")
  accessStatus Int?    @map("access_status")
  status       Int?
  limitUseErrStatus Int?
  creditLimit  Int?    @map("credit_limit")
  remainLimit  Int?    @map("remain_limit")
  minLoan      Int?    @map("min_loan")
  repayDay     Int?    @map("repay_day")
  productInfos Json?   @map("product_infos")
  loanOrders   Json?   @map("loan_orders")
  coupons      Json?   @map("coupons")
  signInfo Json? @map("sign_info")
  reofferInfo Json? @map("reoffer_info")
  discountAmountInfo Json? @map("discount_amountInfo")
  smsInfo Json? @map("sms_info")
  modifyPhone Json? @map("modify_phone")
  repayResultStatus Int? @map("repay_status")
}

model loan_user {
  id       Int     @id @default(autoincrement())
  userId   String  @map("user_id")
  realName String @map("real_name")
  ctfCode  String @map("ctf_code")
  mobileNo String @map("mobile_no")
  creditInfo Json? @map("credit_info")
  bankCardInfo Json? @map("back_card_info")
  loanInfo Json? @map("loan_info")
  releatedUserIds Json? @map("releated_user_ids")
  createTime BigInt @map("create_time")
}

model interface_log {
  id            Int    @id @default(autoincrement())
  userId        Int   @map("user_id")
  interfaceName String @map("interface_name")
  requestData   Json  @map("request_data")
  responseData  Json  @map("response_data")
  createTime    DateTime @map("create_time")
}

model event_tracking {
  id            Int    @id @default(autoincrement())
  userId        Int   @map("user_id")
  userName      String   @map("user_name")
  eventName     String @map("event_name")
  createTime    DateTime @map("create_time")
}

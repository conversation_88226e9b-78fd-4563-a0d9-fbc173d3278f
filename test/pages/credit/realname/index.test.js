import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import Index from "../../../../src/pages/credit/realname/index.vue"; // 根据实际路径调整

describe("Index Component", () => {
  let pinia;

  beforeEach(() => {
    pinia = createPinia();
  });

  it("should render correctly", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    expect(wrapper.exists()).toBe(true);
  });

  it("should initialize the component with correct default values", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;
    vm.data.realNameInfo.realName = "";
    vm.data.realNameInfo.ctfCode = "";
    vm.data.realNameInfo.mobileNo = "";
    expect(vm.flagFirst).toEqual({});
    expect(vm.data.realNameInfo.realName).toBe("");
    expect(vm.data.realNameInfo.ctfCode).toBe("");
    expect(vm.data.realNameInfo.mobileNo).toBe("");
    expect(vm.data.buttonDiable).toBe(true);
  });

  it("should update button disable status based on input fields", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Simulate input changes
    vm.data.realNameInfo.realName = "John Doe";
    vm.data.realNameInfo.ctfCode = "123456789012345678";
    vm.data.realNameInfo.mobileNo = "13800138000";
    vm.data.nameErrorMessage = "";
    vm.data.idCardErrorMessage = "";
    vm.data.phoneErrorMessage = "";
    vm.data.codeErrorMessage = "";
    vm.data.isSmsCode = true;
    vm.data.smsCode = true;

    // Trigger reactivity
    await vm.$nextTick();

    expect(vm.data.buttonDiable).toBe(false);

    // Reset one field to check if button gets disabled again
    vm.data.realNameInfo.realName = "";

    // Trigger reactivity
    await vm.$nextTick();

    expect(vm.data.buttonDiable).toBe(true);
  });

  it("should handle agreement signing", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Mock the request function
    vi.spyOn(vm, "request").mockResolvedValueOnce({
      message: "success",
    });

    await vm.agreeProtocal();
    console.log("vm.agreeProtocal", vm.agreeProtocal);
    expect(vm.request).toHaveBeenCalledWith("/general/api/ams/user/signAgreement", {
      userId: vm.store.param.userId,
      signInfo: [
        {
          agrType: "USER_PROTOCAL_CODE",
          country: "CN",
          branchId: 0,
          language: "zh-CN",
          isAgree: true,
        },
        {
          agrType: "PRIVACY_PROTOCAL_CODE",
          country: "CN",
          branchId: 0,
          language: "zh-CN",
          isAgree: true,
        },
      ],
      deviceInfo: {
        deviceId: "",
        deviceModel: vm.store.param.deviceModel,
        deviceType: "",
      },
    });
  });

  it("should navigate back when clicking the back button", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Mock the back function
    vi.spyOn(vm, "back");

    await vm.backClick();

    expect(vm.back).toHaveBeenCalled();
  });

  it("should validate real name input", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Test valid name
    vm.nameBlur({ target: { value: "John Doe" } });
    expect(vm.data.nameErrorMessage).toBe("");

    // Test invalid name
    vm.nameBlur({ target: { value: "John" } });
    expect(vm.data.nameErrorMessage).toBe("请输入正确的姓名");
  });

  it("should validate ID card input", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Test valid 18-digit ID card
    vm.idCardBlur({ target: { value: "123456789012345678" } });
    expect(vm.data.idCardErrorMessage).toBe("");

    // Test invalid ID card
    vm.idCardBlur({ target: { value: "12345678901234567" } });
    expect(vm.data.idCardErrorMessage).toBe("请输入正确的身份证号码");
  });

  it("should validate phone number input", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const vm = wrapper.vm;

    // Test valid phone number
    vm.phoneBlur({ target: { value: "13800138000" } });
    expect(vm.data.phoneErrorMessage).toBe("");

    // Test invalid phone number
    vm.phoneBlur({ target: { value: "1234567890" } });
    expect(vm.data.phoneErrorMessage).toBe("请输入正确的手机号");
  });
});

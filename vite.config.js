import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import cdn from "vite-plugin-cdn-import";

// https://vitejs.dev/config/
const plugins = [vue()];
//仅处理客户端打包，该插件不支持服务端打包

export default defineConfig({
  base: "/wallet-loan-web/pages",
  plugins,
  // test节点为Vitest的配置项
  test: {
    environment: "jsdom",
    watch: false,
    // reporters: "html",
    // outputFile: "./report/index.html",
  },
});

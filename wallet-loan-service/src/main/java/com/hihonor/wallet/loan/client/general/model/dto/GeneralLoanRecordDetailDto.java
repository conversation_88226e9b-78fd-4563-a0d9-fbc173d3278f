/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general.model.dto;

import java.util.List;

import com.hihonor.wallet.loan.model.dto.ContractDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanTermDto;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
public class GeneralLoanRecordDetailDto {
    /**
     * 渠道方借款订单号
     */
    private String outOrderNo;

    /**
     * 荣耀侧借款申请订单号
     */
    private String applyNo;

    /**
     * 借款申请日期
     */
    private String applyDate;

    /**
     * 借款申请时间戳
     */
    private Long applyTime;

    /**
     * 借款起息日
     */
    private String effectiveDate;

    /**
     * 应还本金总额,单位：分
     */
    private Long loanAmount;

    /**
     * 状态
     * APPLYING：申请中
     * PAYING：正常还款中
     * OVERDUE：已逾期
     * PAYOFF：已结清
     * FAIL：失败（含拒绝）
     *
     * 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败
     */
    private Integer status;

    /**
     * 总期数
     */
    private Integer totalTerm;

    /**
     * 还款方式，
     * 1-等额本息，2-等额本金，3-先息后本，4-锁期
     */
    private Integer repayMethod;

    /**
     * 借款来源，appId
     */
    private String loanSource;

    /**
     * 机构名称，多个资方以"&"分割
     */
    private String institutionNames;

    /**
     * 结清时间(未结清：0)，毫秒
     */
    private Long clearTime;

    /**
     * 已还总金额，单位：分
     */
    private Long paidAmount;

    /**
     * 已还本金总额，单位：分
     */
    private Long paidPrinAmount;

    /**
     * 已还利息总额，单位：分
     */
    private Long paidInterAmount;

    /**
     * 已还服务费总额，单位：分
     */
    private Long paidServiceFee;

    /**
     * 已还罚息，单位：分
     */
    private Long paidPenalty;

    /**
     * 收款卡号（后四位）
     */
    private String bindCardNo;

    /**
     * 收款卡发卡行code
     */
    private String bindBankCode;

    /**
     * 收款卡发卡行名称
     */
    private String bindBankName;

    /**
     * 优惠券id
     */
    private String couponNo;

    /**
     * 日利率
     */
    private String dayRate;

    /**
     * 月利率
     */
    private String monthRate;

    /**
     * 年利率
     */
    private String apr;

    /**
     * 优惠金额，单位：分
     */
    private Long reductionAmount;

    /**
     * 提前还款违约金，单位：分
     */
    private Long prePenalty;

    /**
     * 还款计划列表
     */
    private List<RepayPlanTermDto> repayPlanTerms;

    /**
     * 已签署借款协议列表
     */
    private List<ContractDto> contractList;

    /**
     *借据逾期天数
     */
    private Integer overdueDays;

    /**
     * 借据逾期未还金额
     */
    private Long overdueAmount;
}

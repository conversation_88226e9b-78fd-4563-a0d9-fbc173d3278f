/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.controller;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.loan.model.dto.*;
import com.hihonor.wallet.loan.model.param.*;
import com.hihonor.wallet.loan.service.RepayService;
import io.swagger.annotations.ApiOperation;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@RestController
@RequestMapping("/api/repay")
public class RepayController {
    @Autowired
    private RepayService repayService;

    /**
     * 还款试算
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "还款试算")
    @PostMapping("/trial")
    public ResponseResult<RepayTrialResponse> repayTrial(@RequestBody @Validated RepayTrialParam param) {
        String redisKey = DigestUtils.sha256Hex(RequestUtils.getUid() + "_" + RequestUtils.getMobileNo());
        RepayTrialResponse repayTrialResponse = repayService.repayTrial(param, redisKey);
        return ResponseResult.success(repayTrialResponse);
    }

    /**
     * 重新签约
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "重新签约")
    @PostMapping("/v2/sendVerifyCode")
    public ResponseResult<RepayCheckResponse> resignCheck(@RequestBody @Validated RepayCheckParam param) {
        // todo
        String redisKey = DigestUtils.sha256Hex(RequestUtils.getUid() + "_" + RequestUtils.getMobileNo());
        RepayCheckResponse repayTrialResponse = repayService.repayCheck(param, redisKey);
        return ResponseResult.success(repayTrialResponse);
    }

    /**
     * 主动还款
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "主动还款")
    @PostMapping("/submit")
    public ResponseResult<RepayResponse> repayDo(@RequestBody @Validated RepayParam param) {
        String redisKey = DigestUtils.sha256Hex(RequestUtils.getUid() + "_" + RequestUtils.getMobileNo());
        RepayResponse repayResponse = repayService.repayDo(param, redisKey);
        return ResponseResult.success(repayResponse);
    }

    /**
     * 查询还款结果
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "查询还款结果")
    @PostMapping("/queryStatus")
    public ResponseResult<QueryRepayResultResponse> queryRepayResult(@RequestBody @Validated QueryRepayResultParam param) {
        String redisKey = DigestUtils.sha256Hex(RequestUtils.getUid() + "_" + RequestUtils.getMobileNo());
        QueryRepayResultResponse queryRepayResultResponse = repayService.queryRepayResult(param, redisKey);
        return ResponseResult.success(queryRepayResultResponse);
    }

    /**
     * 还款计划查询
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "还款计划查询")
    @PostMapping("/plan")
    public ResponseResult<List<RepayPlanDto>> queryRepayPlan(@RequestBody @Validated RepayPlanParam param) {
        List<RepayPlanDto> repayPlanDtoList = repayService.queryRepayPlan(param);
        return ResponseResult.success(repayPlanDtoList);
    }

    /**
     * 还款记录列表
     *
     * @param param param
     * @return
     */
    @ApiOperation(value = "还款记录列表")
    @PostMapping("/record")
    public ResponseResult<List<RepayDto>> repayRecord(@RequestBody @Validated RepayRecordParam param) {
        List<RepayDto> repayDtoList = repayService.repayRecord(param);
        return ResponseResult.success(repayDtoList);
    }
}

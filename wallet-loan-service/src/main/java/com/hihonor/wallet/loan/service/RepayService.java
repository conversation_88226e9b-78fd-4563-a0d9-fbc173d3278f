/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hihonor.wallet.loan.entity.LoanRepayEntity;
import com.hihonor.wallet.loan.model.dto.*;
import com.hihonor.wallet.loan.model.param.*;

import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
public interface RepayService extends IService<LoanRepayEntity> {
    /**
     * 还款试算
     *
     * @param param param
     * @return RepayTrialResponse
     * @param redisLockPostName redisLockPostName
     */
    RepayTrialResponse repayTrial(RepayTrialParam param, String redisLockPostName);

    /**
     * 还款前检查 （发送短信）
     *
     * @param param param
     * @return RepayCheckDto
     * @param redisLockPostName redisLockPostName
     */
    RepayCheckResponse repayCheck(RepayCheckParam param, String redisLockPostName);

    /**
     * 主动退款
     *
     * @param param param
     * @return RepayResponse
     * @param redisLockPostName redisLockPostName
     */
    RepayResponse repayDo(RepayParam param, String redisLockPostName);

    /**
     * 还款状态查询
     *
     * @param param param
     * @return QueryRepayStatusResponse
     * @param redisLockPostName redisLockPostName
     */
    QueryRepayResultResponse queryRepayResult(QueryRepayResultParam param, String redisLockPostName);


    /**
     * 还款计划查询
     *
     * @param param param
     * @return
     */
    List<RepayPlanDto> queryRepayPlan(RepayPlanParam param);

    /**
     * 还款记录列表
     *
     * @param param param
     * @return
     */
    List<RepayDto> repayRecord(RepayRecordParam param);
}

/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general.model.dto;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
public class GeneralRepayPlanItems {
    /**
     * 期次
     */
    private Integer termNo;

    /**
     * 应还款日期
     */
    private String shouldRepayDate;

    /**
     * 本期应还总额,单位：分
     */
    private Long termAmount;

    /**
     * 本期应还本金，单位：分
     */
    private Long termPrincipal;

    /**
     * 本期应还利息，单位：分
     */
    private Long termInterest;

    /**
     * 本期应还服务费，单位：分
     */
    private Long termFee;

    /**
     * 本期优惠券减免金额，单位：分
     */
    private Long termReductionAmount;

    /**
     * 应还金额（总金额 - 贴息金额 - 优惠券金额）
     */
    private Long amount;

    /**
     * 本期应还罚息
     */
    private Long termPenalty;

    /**
     * 本期应还逾期费
     */
    private Long termOverdueFee;

    /**
     * 本期应还违约金
     */
    private Long termViolateFee;

    /**
     * 本期应还账务管理费
     */
    private Long termMgntFee;

    /**
     * 本期应还服务费
     */
    private Long termServiceFee;

    /**
     * 本期应还本金罚息，单位：分
     */
    private Long termPrinPenalty;

    /**
     * 本期应还利息罚息，单位：分
     */
    private Long termInterPenalty;

    /**
     * 逾期天数
     */
    private Integer overdueDays;


    /**
     * 当期状态
     * 1-已结清，2-待还款，3-部分已还，4-逾期，5-宽限期
     */
    private Integer termStatus;

    /**
     * 还款类型	1：银行卡主动还款 2：银行卡代扣
     */
    private Integer repayCategory;

    /**
     * 本期应还费用，单位：分
     */
    private Long termCharges;

    /**
     * 本期应还违约金，单位：分
     */
    private Long violateFee;


    /**
     * 本期实际还款日
     */
    private Long paidTime;

    /**
     * 本期实还总额，单位：分
     */
    private Long paidTermAmount;

    /**
     * 本期实还本金，单位：分
     */
    private Long paidTermPrincipal;

    /**
     * 本期实还利息，单位：分
     */
    private Long paidTermInterest;

    /**
     * 本期实还费用，单位：分
     */
    private Long paidTermCharges;

    /**
     * 本期实还优惠券额，单位：分
     */
    private Long paidTermReductionAmount;

    /**
     * 本期实还本金罚息，单位：分
     */
    private Long paidTermPrinPenalty;

    /**
     * 本期实还利息罚息，单位：分
     */
    private Long paidTermInterPenalty;

    /**
     * 本期实还逾期费，单位：分
     */
    private Long paidTermOverdueFee;

    /**
     * 本期实还服务费，单位：分
     */
    private Long paidTermFee;

    /**
     * 本期实还服务费，单位：分
     */
    private Long paidTermServiceFee;

    /**
     * 本期实还违约金，单位：分
     */
    private Long paidViolateFee;

    /**
     * 剩余应还还款金额，单位：分
     */
    private Long payableTermAmount;

    /**
     * 剩余应还还款本金，单位：分
     */
    private Long payableTermPrincipal;

    /**
     * 剩余应还还款利息，单位：分
     */
    private Long payableTermInterest;

    /**
     * 剩余应还还款费用，单位：分
     */
    private Long payableTermCharges;

    /**
     * 剩余应还本金罚息，单位：分
     */
    private Long payableTermPrinPenalty;

    /**
     * 剩余应还利息罚息，单位：分
     */
    private Long payableTermInterPenalty;

    /**
     * 剩余应还逾期费，单位：分
     */
    private Long payableTermOverdueFee;

    /**
     * 剩余应还管理费，单位：分
     */
    private Long payableTermFee;

    /**
     * 剩余应还服务费，单位：分
     */
    private Long payableTermServiceFee;

    /**
     * 剩余应还违约金，单位：分
     */
    private Long payableViolateFee;

    /**
     * 逾期金额，单位：分
     */
    private Long overdueAmt;

    /**
     * 提前还款标志	Y/N
     */
    private String preRepay;

    /**
     * 逾期标志
     */
    private String overdue;

}

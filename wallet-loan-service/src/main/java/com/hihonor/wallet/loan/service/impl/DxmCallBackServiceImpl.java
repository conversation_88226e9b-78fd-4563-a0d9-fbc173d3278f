/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import cn.hutool.log.Log;
import com.hihonor.wallet.common.constant.CommonConstant;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.kafka.KafkaMsgType;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.report.RealTimeReportInfo;
import com.hihonor.wallet.common.report.ReportEvent;
import com.hihonor.wallet.common.report.ReportLog;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.TimeUtils;
import com.hihonor.wallet.common.util.UUIDGenerator;
import com.hihonor.wallet.common.util.ValidationUtils;
import com.hihonor.wallet.common.util.log.SensitiveLog;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmAllCouponListDto;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmCouponDetail;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmRateAdjustInfo;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmTmpPriceInfo;
import com.hihonor.wallet.loan.client.general.constant.GeneralConstant;
import com.hihonor.wallet.loan.client.general.model.dto.GeneralLoanRecordDetailDto;
import com.hihonor.wallet.loan.client.general.model.param.GeneralRepayResultNotifyParam;
import com.hihonor.wallet.loan.client.model.dto.QueryUserCreditInfoDto;
import com.hihonor.wallet.loan.client.model.param.QueryRepayPlanParam;
import com.hihonor.wallet.loan.client.model.param.QueryUserCreditInfoParam;
import com.hihonor.wallet.loan.entity.CouponNotifyEntity;
import com.hihonor.wallet.loan.entity.CreditApplyEncryptInfo;
import com.hihonor.wallet.loan.entity.CreditApplyRecordEntity;
import com.hihonor.wallet.loan.entity.LoanRepayEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanRepayRecordEntity;
import com.hihonor.wallet.loan.entity.LoanRepayResultEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanRepayTermEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanRepayTermRecordEntity;
import com.fasterxml.jackson.core.type.TypeReference;
import com.hihonor.wallet.loan.entity.CreditChangeEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanApplyEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanRecordEntity;
import com.hihonor.wallet.loan.entity.LoanTermEncryptInfo;
import com.hihonor.wallet.loan.entity.LoanTermRecordEntity;
import com.hihonor.wallet.loan.enums.PushMessageTypeEnums;

import com.hihonor.wallet.loan.mapper.service.LoanRecordService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.util.IdUtils;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.loan.client.dxm.DxmApi;
import com.hihonor.wallet.loan.client.dxm.DxmWrapClient;
import com.hihonor.wallet.loan.client.dxm.constant.DxmConstant;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmBaseDto;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmLoanRecordDetailDto;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmRepayPlanItems;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmRepayResultList;
import com.hihonor.wallet.loan.client.dxm.model.param.*;
import com.hihonor.wallet.loan.client.model.dto.QueryCreditApplyResultDto;
import com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto;
import com.hihonor.wallet.loan.client.model.dto.RepayItems;
import com.hihonor.wallet.loan.client.model.dto.RepayResultList;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.CreditApplyEntity;
import com.hihonor.wallet.loan.entity.CreditChangeEntity;
import com.hihonor.wallet.loan.entity.LoanApplyEntity;
import com.hihonor.wallet.loan.entity.LoanRepayEntity;
import com.hihonor.wallet.loan.entity.LoanRepayResultEntity;
import com.hihonor.wallet.loan.entity.LoanRepayTermEntity;
import com.hihonor.wallet.loan.entity.LoanTermEntity;
import com.hihonor.wallet.loan.entity.LoanUserEntity;
import com.hihonor.wallet.loan.kafka.LoanKafkaProducer;
import com.hihonor.wallet.loan.mapper.CouponNotifyMapper;
import com.hihonor.wallet.loan.mapper.CreditApplyMapper;
import com.hihonor.wallet.loan.mapper.CreditApplyRecordMapper;
import com.hihonor.wallet.loan.mapper.CreditChangeMapper;
import com.hihonor.wallet.loan.mapper.LoanApplyMapper;
import com.hihonor.wallet.loan.mapper.LoanRecordMapper;
import com.hihonor.wallet.loan.mapper.LoanRepayMapper;
import com.hihonor.wallet.loan.mapper.LoanRepayRecordMapper;
import com.hihonor.wallet.loan.mapper.LoanRepayResultMapper;
import com.hihonor.wallet.loan.mapper.LoanRepayTermMapper;
import com.hihonor.wallet.loan.mapper.LoanRepayTermRecordMapper;
import com.hihonor.wallet.loan.mapper.LoanTermMapper;
import com.hihonor.wallet.loan.mapper.LoanTermRecordMapper;
import com.hihonor.wallet.loan.mapper.LoanUserMapper;
import com.hihonor.wallet.loan.model.dto.LoanSupplierDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanDto;
import com.hihonor.wallet.loan.model.param.CouponNotifyParam;
import com.hihonor.wallet.loan.model.param.NotifyCreditChangeStatusParam;
import com.hihonor.wallet.loan.model.param.PollingOrder;
import com.hihonor.wallet.loan.service.CreditService;
import com.hihonor.wallet.loan.service.DxmCallBackService;
import com.hihonor.wallet.loan.service.PushMessageService;
import com.hihonor.wallet.loan.service.UserService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Stream;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Service
public class DxmCallBackServiceImpl implements DxmCallBackService {

    @Autowired
    private DxmApi dxmApi;

    @Autowired
    private CreditApplyMapper creditApplyMapper;

    @Autowired
    private LoanRepayTermMapper loanRepayTermMapper;

    @Autowired
    private LoanRepayResultMapper loanRepayResultMapper;

    @Autowired
    private LoanRepayTermRecordMapper loanRepayTermRecordMapper;

    @Autowired
    private LoanRepayMapper loanRepayMapper;

    @Autowired
    private LoanRepayRecordMapper loanRepayRecordMapper;

    @Autowired
    private LoanUserMapper loanUserMapper;

    @Autowired
    private CreditChangeMapper creditChangeMapper;

    @Autowired
    private CreditApplyRecordMapper creditApplyRecordMapper;

    @Autowired
    private LoanApplyMapper loanApplyMapper;

    @Autowired
    private LoanTermMapper loanTermMapper;

    @Autowired
    private LoanTermRecordMapper loanTermRecordMapper;

    @Autowired
    private PushMessageService pushMessageService;

    @Autowired
    private CouponNotifyMapper couponNotifyMapper;

    @Autowired
    private LoanRecordMapper loanRecordMapper;

    @Autowired
    private LoanClient loanClient;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private ReportLog reportLog;

    @Autowired
    private LoanKafkaProducer loanKafkaProducer;

    @Autowired
    private LoanRecordService loanRecordService;

    public static final String PUSH_TYPE_LOAN_SUCCESS = "LoanSuccess";

    public static final String PUSH_TYPE_LOAN_FAILURE = "LetterFailure";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public DxmBaseDto creditApplyNotify(String notifyBody) {
        DxmBaseDto dto = new DxmBaseDto();
        DxmBaseParam notifyParam;
        DxmCreditApplyResultNotifyParam param;
        try {
            notifyParam = dxmApi.getEncryptDataForNotify(notifyBody);
            param = JsonUtils.parse(String.valueOf(notifyParam.getParams()), DxmCreditApplyResultNotifyParam.class);
        } catch (BusinessException e) {
            buildResponse(dto, e);
            return dto;
        }
        LogUtil.runSensitiveInfoLog("creditApplyNotify : {}", param);

        if (param == null) {
            buildResponse(dto, new BusinessException("creditApplyNotify转换后参数为空"));
            return dto;
        }
        if (StringUtils.isBlank(param.getApplyNo()) || StringUtils.isBlank(param.getOpenId())
                || StringUtils.isBlank(param.getApplyResult()) || StringUtils.isBlank(param.getOutOrderNo())) {
            buildResponse(dto, new BusinessException("必传参数为空"));
            return dto;
        }

        LoanUserEntity userInfo = getUserInfo(param.getOpenId());
        if (Objects.isNull(userInfo)) {
            buildResponse(dto, new BusinessException("openid 没有对应的用户准入信息"));
            return dto;
        }
        LogUtil.runInfoLog("该用户userId为：{}", SensitiveLog.hideMarkStr(String.valueOf(userInfo.getUserId())));

        LambdaQueryWrapper<CreditApplyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CreditApplyEntity::getApplyNo, param.getApplyNo())
                .eq(CreditApplyEntity::getOutOpenId, param.getOpenId())
                .orderByDesc(CreditApplyEntity::getCreateTime).last("LIMIT 1");
        CreditApplyEntity creditApplyEntity = creditApplyMapper.selectOne(lambdaQueryWrapper);
        if (creditApplyEntity == null) {
            buildResponse(dto, new BusinessException("applyNo 没有对应的授信信息"));
            return dto;
        }

        QueryCreditApplyResultDto queryCreditApplyResultDto = new QueryCreditApplyResultDto();
        BeanUtils.copyProperties(param, queryCreditApplyResultDto);
        queryCreditApplyResultDto.setIdentity(covertIdentify(param.getIdentity()));
        queryCreditApplyResultDto.setApplyStatus(covertApplyResult(param.getApplyResult()));
        if (param.getTempLimitInfo() != null) {
            queryCreditApplyResultDto.setTempLimitValidTime(param.getTempLimitInfo().getValidTime());
            queryCreditApplyResultDto.setTempLimitCredit(String.valueOf(param.getTempLimitInfo().getCreditLimit()));
            queryCreditApplyResultDto.setTempLimitAvailable(String.valueOf(param.getTempLimitInfo().getAvailableLimit()));
        }
        if (param.getRefuseInfo() != null) {
            queryCreditApplyResultDto.setRefuseCode(param.getRefuseInfo().getCode());
//            queryCreditApplyResultDto.setRefuseMsg(param.getRefuseInfo().getMessage());
        }
        queryCreditApplyResultDto.setRefuseMsg(param.getRefuseMsgData());

        if (DxmConstant.LimitType.CIRCLE.equals(param.getLimitType())) {
            queryCreditApplyResultDto.setLimitType(LoanConstant.LimitType.CIRCLE);
        }

        queryCreditApplyResultDto.setTempPriceValidTime(param.getTempPriceDueTime());
        Map<String, String> rate = null;
        Map<String, String> rateMap = new HashMap<>();
        if (LoanConstant.CreditStatus.APPROVED == queryCreditApplyResultDto.getApplyStatus()
                || LoanConstant.CreditStatus.REJECT == queryCreditApplyResultDto.getApplyStatus()) {
            CreditApplyEntity creditEntity = new CreditApplyEntity();
            CreditApplyEncryptInfo info = new CreditApplyEncryptInfo();
            BeanUtils.copyProperties(queryCreditApplyResultDto, info);
            BeanUtils.copyProperties(queryCreditApplyResultDto, creditEntity);
            creditEntity.setIdentity(DxmWrapClient.covertIdentify(param.getIdentity()));
            creditEntity.setLimitInfo(EncryptUtil.encrypt(JsonUtils.toJson(info)));
            LambdaUpdateWrapper<CreditApplyEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(CreditApplyEntity::getApplyNo, param.getApplyNo());
            lambdaUpdateWrapper.ne(CreditApplyEntity::getApplyStatus, LoanConstant.CreditStatus.APPROVED);
            if (LoanConstant.CreditStatus.REJECT == queryCreditApplyResultDto.getApplyStatus()) {
                LoanSupplierDto loanSupplierDto = userService.getCurrentSupplierInfo(userInfo.getSupplier());
                creditEntity.setLockTime(TimeUtils.getAfterAnotherDaysDate(loanSupplierDto.getCreditFailLockTime()));
            }
            creditApplyMapper.update(creditEntity, lambdaUpdateWrapper);
            CreditApplyRecordEntity creditApplyRecordEntity = new CreditApplyRecordEntity();
            LambdaUpdateWrapper<CreditApplyRecordEntity> creditApplyRecordUpdateWrapper = new LambdaUpdateWrapper<>();
            creditApplyRecordUpdateWrapper.eq(CreditApplyRecordEntity::getApplyNo, param.getApplyNo());
            creditApplyRecordUpdateWrapper.ne(CreditApplyRecordEntity::getApplyStatus, LoanConstant.CreditStatus.APPROVED);
            BeanUtils.copyProperties(creditEntity, creditApplyRecordEntity);
            BeanUtils.copyProperties(queryCreditApplyResultDto, creditApplyRecordEntity);
            if (LoanConstant.CreditStatus.APPROVED == queryCreditApplyResultDto.getApplyStatus()) {
                rate = getApr(param.getUserId(), param.getOpenId(), LoanConstant.Supplier.DXM, rateMap);
                LogUtil.runInfoLog("对应的rateMap为:{}", rateMap.toString());
                creditApplyRecordEntity.setApr(rateMap.get(LoanConstant.APR) == null ? "" : rateMap.get(LoanConstant.APR));
                creditApplyRecordEntity.setDayRate(rateMap.get(LoanConstant.DAY_RATE) == null ? "" : rateMap.get(LoanConstant.DAY_RATE));
                creditApplyRecordEntity.setTempApr(rateMap.get(LoanConstant.TEMP_APR) == null ? "" : rateMap.get(LoanConstant.TEMP_APR));
                creditApplyRecordEntity.setTempDayRate(rateMap.get(LoanConstant.TEMP_DAY_RATE) == null ? "" : rateMap.get(LoanConstant.TEMP_DAY_RATE));
            }
            creditApplyRecordMapper.update(creditApplyRecordEntity, creditApplyRecordUpdateWrapper);

        }
        String type = LoanConstant.CreditStatus.APPROVED == queryCreditApplyResultDto.getApplyStatus()
                ? PushMessageTypeEnums.CreditSuccess.getStringValue() : PushMessageTypeEnums.CreditFailure.getStringValue();
        Map<String, String> map = null;
        if (StringUtils.equals(type, PushMessageTypeEnums.CreditSuccess.getStringValue())) {
            rate = rate == null ? getApr(param.getUserId(), param.getOpenId(), LoanConstant.Supplier.DXM, null) : rate;
            map = new HashMap<>();
            map.put("limit", getAmount(queryCreditApplyResultDto.getRemainLimit()));
            map.put("amount", queryCreditApplyResultDto.getRemainLimit() == null ? "" : queryCreditApplyResultDto.getRemainLimit().toString());
            map.put("annualRate", rate.get(LoanConstant.APR));
        }
        MDC.put(CommonConstant.MDC_USERID, userInfo.getUserId().toString());
        MDC.put(CommonConstant.MDC_DEVICEID, userInfo.getDeviceId());
        if (LoanConstant.CreditStatus.APPROVED == queryCreditApplyResultDto.getApplyStatus()) {
            userService.deleteEntryInfoCache(Long.toString(userInfo.getUserId()));
            reportLog.reportSuccessForLoan(ReportEvent.LOAN_CREDIT_SUCCESS, LoanConstant.Supplier.DXM,
                    userService.getReportInfo(userService.getPollingOrder(userInfo.getFlowNo(), userInfo.getUserId().toString())),
                    RequestUtils.getTraceId(), String.valueOf(userInfo.getUserId()), userInfo.getDeviceId(), userInfo.getDeviceModel());
            loanKafkaProducer.sendKafkaMessage(null, creditApplyEntity.getOaid(), KafkaMsgType.CREDIT,
                    LoanConstant.AdsConversionId.CREDIT, creditApplyEntity.getCid(),
                    creditApplyEntity.getSubCid(), creditApplyEntity.getLoc());
            userService.delPollingOrder(userInfo.getFlowNo(), userInfo.getUserId().toString());
        } else if (LoanConstant.CreditStatus.REJECT == queryCreditApplyResultDto.getApplyStatus()) {
            reportLog.reportFailForLoan(ReportEvent.LOAN_CREDIT_FAIL, LoanConstant.Supplier.DXM,
                RequestUtils.getTraceId(), param.getRefuseInfo().getCode(), param.getRefuseInfo().getMessage(),
                    userService.getReportInfo(userService.getPollingOrder(userInfo.getFlowNo(), userInfo.getUserId().toString())),
                    String.valueOf(userInfo.getUserId()), userInfo.getDeviceId(), userInfo.getDeviceModel());
            PollingOrder pollingOrder = userService.updatePollingOrder(userInfo.getFlowNo(), userInfo.getUserId().toString(), userInfo.getSupplier());
            LambdaUpdateWrapper<LoanUserEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(LoanUserEntity::getUserId, userInfo.getUserId())
                    .set(LoanUserEntity::getInUse, LoanConstant.InUse.UN_USE);
            userService.update(lambdaUpdateWrapper);
            if (Objects.nonNull(pollingOrder) && Objects.isNull(pollingOrder.getCurrentSupplier())) {
                userService.delPollingOrder(userInfo.getFlowNo(), userInfo.getUserId().toString());
                LogUtil.runInfoLog("没有可用CP");
            }
        }

        sendPushMessage(Long.toString(userInfo.getUserId()), type, map);
        buildResponse(dto, null);
        return dto;
    }

    @Override
    public DxmBaseDto repayResultNotify(String notifyBody) {
        DxmBaseDto dto = new DxmBaseDto();
        DxmBaseParam notifyParam;
        DxmRepayResultNotifyParam param;
        QueryRepayResultDto queryRepayResultDto;
        LoanUserEntity loanUserEntity;
        try {
            notifyParam = dxmApi.getEncryptDataForNotify(notifyBody);
            param = JsonUtils.parse(String.valueOf(notifyParam.getParams()), DxmRepayResultNotifyParam.class);
            LogUtil.runSensitiveInfoLog("repayResultNotify {}", param);
            if (param == null) {
                buildResponse(dto, new BusinessException("repayResultNotify转换后参数为空"));
                return dto;
            }
            if (StringUtils.isBlank(param.getOpenId()) || StringUtils.isBlank(param.getRepaySource())
                    || StringUtils.isBlank(param.getRepayStatus()) || CollectionUtil.isEmpty(param.getRepayResultList())) {
                buildResponse(dto, new BusinessException("必传参数为空"));
                return dto;
            }

            loanUserEntity = getUserInfo(param.getOpenId());
            if (Objects.isNull(loanUserEntity)) {
                buildResponse(dto, new BusinessException("openid 没有对应的用户准入信息"));
                return dto;
            }
            LogUtil.runInfoLog("该用户userId为：{}", SensitiveLog.hideMarkStr(String.valueOf(loanUserEntity.getUserId())));

            queryRepayResultDto = new QueryRepayResultDto();
            BeanUtils.copyProperties(param, queryRepayResultDto);
            List<RepayResultList> repayResultList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(param.getRepayResultList())) {
                param.getRepayResultList().forEach(item -> {
                    RepayResultList result = new RepayResultList();
                    BeanUtils.copyProperties(item, result);
                    result.setStatus(DxmWrapClient.covertRepayStatus(item.getStatus()));
                    List<RepayItems> repayItems = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(item.getRepayItems())) {
                        item.getRepayItems().forEach(dItem -> {
                            RepayItems repayItem = new RepayItems();
                            BeanUtils.copyProperties(dItem, repayItem);
                            repayItems.add(repayItem);
                        });
                    }
                    result.setRepayItems(repayItems);
                    repayResultList.add(result);
                });
                queryRepayResultDto.setRepayResultList(repayResultList);
            }
            queryRepayResultDto.setRepayStatus(DxmWrapClient.covertRepayStatus(param.getRepayStatus()));
            if (Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, queryRepayResultDto.getRepayStatus())
                    || Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, queryRepayResultDto.getRepayStatus())) {
                userService.deleteEntryInfoCache(Long.toString(loanUserEntity.getUserId()));
            }
            updateRepayResult(queryRepayResultDto, param);
        } catch (BusinessException e) {
            buildResponse(dto, e);
            return dto;
        }
        //t_loan_apply状态确认
        List<DxmLoanRecordDetailDto> detailDtoList = applyStatusChange(param);
        LogUtil.runSensitiveInfoLog("DxmLoanRecordDetailDto {}", detailDtoList);
        repayRealTimeReport(param, loanUserEntity, queryRepayResultDto);
        covertRepayPushTye(detailDtoList, param);
        buildResponse(dto, null);
        return dto;
    }

    private void updateLoanTermRecord(String outOrderNo, List<DxmRepayPlanItems> repayPlanItems) {
        if (repayPlanItems != null) {
            for (DxmRepayPlanItems item : repayPlanItems) {
                LoanTermRecordEntity loanTermRecordEntity = getLoanTermRecordEntity(item);
                loanTermRecordMapper.update(loanTermRecordEntity, new LambdaQueryWrapper<LoanTermRecordEntity>()
                        .eq(LoanTermRecordEntity::getOutOrderNo, outOrderNo)
                        .eq(LoanTermRecordEntity::getTermNo, item.getTermNo()));
                LoanTermEntity loanTermEntity = getLoanTermEntity(item);
                loanTermMapper.update(loanTermEntity, new LambdaQueryWrapper<LoanTermEntity>()
                        .eq(LoanTermEntity::getOutOrderNo, outOrderNo)
                        .eq(LoanTermEntity::getTermNo, item.getTermNo()));
            }
        }
    }

    /**
     * 新老客标识转换
     *
     * @param identify identify
     * @return Integer
     */
    public static Integer covertIdentify(String identify) {
        if (StringUtils.isBlank(identify)) {
            return null;
        } else if (StringUtils.equals(identify, DxmConstant.Identify.NEW)) {
            return LoanConstant.Identify.NEW;
        }
        return LoanConstant.Identify.OLD;
    }

    private List<DxmLoanRecordDetailDto> applyStatusChange(DxmRepayResultNotifyParam param) {
        LogUtil.runInfoLog("申请回调，确认是否已结清，{}", SensitiveLog.hideMarkStr(param.getUserId()));
        List<DxmRepayResultList> resultList = param.getRepayResultList();
        List<DxmLoanRecordDetailDto> detailDtoList = new ArrayList<>();
        resultList.forEach(item -> {
            String outOrderNo = item.getOutOrderNo();
            if (StringUtils.isEmpty(outOrderNo)) {
                LogUtil.runErrorLog("外部订单号为空，{}", item);
                return;
            }
            DxmLoanRecordDetailParam detailParam = new DxmLoanRecordDetailParam();
            detailParam.setOpenId(param.getOpenId());
            //当外贷场景时，返回的applyno
            if (StringUtils.equals(param.getRepaySource(), "OTHER")) {
                LambdaQueryWrapper<LoanApplyEntity> loanApplyWrapper = new LambdaQueryWrapper<>();
                loanApplyWrapper.eq(LoanApplyEntity::getApplyNo, outOrderNo);
                LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(loanApplyWrapper);
                outOrderNo = loanApplyEntity.getOutOrderNo();
            }
            detailParam.setOutOrderNo(outOrderNo);
            DxmLoanRecordDetailDto dxmLoanRecordDetailDto = dxmApi.loanRecordDetail(detailParam);
            String status = dxmLoanRecordDetailDto.getStatus();
            //已结清状态更新借款表
            if (DxmConstant.LoanTermStatus.PAYOFF.equals(status)) {
                LogUtil.runInfoLog("已结清，更新借款表，{}", SensitiveLog.hideMarkStr(param.getUserId()));
                LoanApplyEntity loanApplyEntity = new LoanApplyEntity();
                loanApplyEntity.setRepayStatus(LoanConstant.LoanTermStatus.SETTLE);
                loanApplyEntity.setClearTime(dxmLoanRecordDetailDto.getClearTime());
                loanApplyMapper.update(loanApplyEntity,
                        new LambdaQueryWrapper<LoanApplyEntity>()
                                .eq(LoanApplyEntity::getOutOrderNo, dxmLoanRecordDetailDto.getOutOrderNo()));
                LoanRecordEntity loanRecordEntity = new LoanRecordEntity();
                loanRecordEntity.setRepayStatus(LoanConstant.LoanTermStatus.SETTLE);
                loanRecordEntity.setClearTime(dxmLoanRecordDetailDto.getClearTime());
                loanRecordMapper.update(loanRecordEntity,
                        new LambdaQueryWrapper<LoanRecordEntity>()
                                .eq(LoanRecordEntity::getOutOrderNo, dxmLoanRecordDetailDto.getOutOrderNo()));
            }
            detailDtoList.add(dxmLoanRecordDetailDto);
            //  更新t_loan_term 和 t_loan_term_record表记录
            updateLoanTermRecord(outOrderNo, dxmLoanRecordDetailDto.getRepayPlanItems());
        });

        return detailDtoList;
    }


    /**
     * 还款实时日志发送
     *
     * @param
     * @param param param
     */
    private void repayRealTimeReport(DxmRepayResultNotifyParam param,
                                     LoanUserEntity loanUserEntity, QueryRepayResultDto queryRepayResultDto) {
        try {
            RealTimeReportInfo realTimeReportInfo = new RealTimeReportInfo();
            realTimeReportInfo.setUserId(loanUserEntity.getUserId().toString());
            realTimeReportInfo.setSupplierId(loanUserEntity.getSupplier());
            realTimeReportInfo.setOrderNo(UUIDGenerator.generate());
            if (StringUtils.isNotEmpty(param.getOutOrderNo())) {
                realTimeReportInfo.setOrderNo(DigestUtils.sha256Hex(param.getOutOrderNo()));
            }
            if (Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, queryRepayResultDto.getRepayStatus())
                    || Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, queryRepayResultDto.getRepayStatus())) {
                QueryUserCreditInfoParam creditInfoParam = new QueryUserCreditInfoParam();
                creditInfoParam.setOpenId(loanUserEntity.getOutOpenId());
                creditInfoParam.setUserId(loanUserEntity.getOpenId());
                creditInfoParam.setSupplier(loanUserEntity.getSupplier());
                realTimeReportInfo.setType(1);
                QueryUserCreditInfoDto queryUserCreditInfoDto = loanClient.queryUserCreditInfo(creditInfoParam);
                if (queryUserCreditInfoDto != null && !Objects.isNull(queryUserCreditInfoDto.getCreditLimit()) && queryUserCreditInfoDto.getCreditLimit() != 0) {
                    realTimeReportInfo.setRepayRadio(String.format("%.2f%%",((double)param.getRepayAmount() / queryUserCreditInfoDto.getCreditLimit()) * 100));
                }
                reportLog.reportSuccessRealTime(realTimeReportInfo);
                List<RepayPlanDto> list = getRepayPlan(loanUserEntity);
                if (CollectionUtils.isEmpty(list)) {
                    realTimeReportInfo.setType(2);
                    realTimeReportInfo.setRepayRadio(null);
                    reportLog.reportSuccessRealTime(realTimeReportInfo);
                }
            }
        } catch (BusinessException e) {
            LogUtil.runErrorLog("repayRealTimeReport {}" + e);
        }
    }


    /**
     * 获取待还款计划
     *
     * @param loanUserEntity RepayPlanDto
     * @return
     */
    private List<RepayPlanDto> getRepayPlan(LoanUserEntity loanUserEntity) {
        QueryRepayPlanParam queryRepayPlanParam = new QueryRepayPlanParam();
        queryRepayPlanParam.setSupplier(loanUserEntity.getSupplier());
        queryRepayPlanParam.setUserId(loanUserEntity.getOpenId());
        queryRepayPlanParam.setOpenId(loanUserEntity.getOutOpenId());
        List<RepayPlanDto> list = loanClient.queryRepayPlan(queryRepayPlanParam);
        return list;
    }

    @Override
    public DxmBaseDto loanResultNotify(String notifyBody) {
        DxmBaseDto dto = new DxmBaseDto();
        DxmBaseParam notifyParam;
        DxmLoanResultNotifyParam param;
        try {
            notifyParam = dxmApi.getEncryptDataForNotify(notifyBody);
            LogUtil.runSensitiveInfoLog("度小满借款结果通知参数:{}", notifyParam);
            param = JsonUtils.parse(String.valueOf(notifyParam.getParams()), DxmLoanResultNotifyParam.class);
        } catch (BusinessException e) {
            buildResponse(dto, e);
            return dto;
        }
        if (StringUtils.isBlank(param.getApplyStatus()) || StringUtils.isBlank(param.getOpenId()) || StringUtils.isBlank(param.getUserId())) {
            throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "applyStatus, openId, userId exist null");
        }
        // 保存借款申请结果至 t_loan_apply
        LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(new LambdaQueryWrapper<LoanApplyEntity>().eq(LoanApplyEntity::getApplyNo, param.getTransNo()));
        if (loanApplyEntity == null) {
            throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "transNo(该借款订单不存在)");
        }
        loanApplyEntity = updateLoanApplyEntity(loanApplyEntity, param);
        if (loanApplyEntity != null) {
            LogUtil.runInfoLog("该用户userId为：{}", SensitiveLog.hideMarkStr(String.valueOf(loanApplyEntity.getUserId())));
            MDC.put(CommonConstant.MDC_USERID, loanApplyEntity.getUserId().toString());
            MDC.put(CommonConstant.MDC_DEVICEID, loanApplyEntity.getDeviceId());
        }
        if (Objects.equals(LoanConstant.LoanStatus.SUCCESS, loanApplyEntity.getApplyStatus())) {
            // 如果使用了优惠券的话，更新优惠券状态
            if (StringUtils.isNotBlank(loanApplyEntity.getCouponNo())){
                couponNotifyMapper.update(null, new LambdaUpdateWrapper<CouponNotifyEntity>().eq(CouponNotifyEntity::getSupplier, loanApplyEntity.getSupplier())
                        .eq(CouponNotifyEntity::getCouponNo, loanApplyEntity.getCouponNo())
                        .eq(CouponNotifyEntity::getUserId, loanApplyEntity.getUserId())
                        .set(CouponNotifyEntity::getCouponStatus, LoanConstant.LoanCouponStatus.USED)
                        .set(CouponNotifyEntity::getUseTime, loanApplyEntity.getApplyTime()));
            }
            userService.deleteEntryInfoCache(loanApplyEntity.getUserId().toString());
            reportLog.reportSuccessForLoan(ReportEvent.LOAN_SUCCESS, LoanConstant.Supplier.DXM, null, RequestUtils.getTraceId(),
                    String.valueOf(loanApplyEntity.getUserId()), loanApplyEntity.getDeviceId(), loanApplyEntity.getDeviceModel());
        } else if (Objects.equals(LoanConstant.LoanStatus.FAIL, loanApplyEntity.getApplyStatus())) {
            reportLog.reportFailForLoan(ReportEvent.LOAN_APPLY_FAIL, LoanConstant.Supplier.DXM, RequestUtils.getTraceId(),
                param.getRefuseCode(), StringUtils.isNotBlank(param.getRefuseMsgData()) ? param.getRefuseMsgData() : null, null,
                    String.valueOf(loanApplyEntity.getUserId()), loanApplyEntity.getDeviceId(), loanApplyEntity.getDeviceModel());
        }
        //  借款成功，调用dxm接口查询借款记录详情（loan.record.detail），保存借款期次信息至t_loan_term
        if (DxmConstant.LoanStatus.SUCCESS.equals(param.getApplyStatus())) {
            DxmLoanRecordDetailParam dxmLoanRecordDetailParam = new DxmLoanRecordDetailParam();
            String userId = String.valueOf(loanApplyEntity.getUserId());
            String outOpenId = loanApplyEntity.getOutOpenId();
            dxmLoanRecordDetailParam.setUserId(userId);
            dxmLoanRecordDetailParam.setOpenId(outOpenId);
            dxmLoanRecordDetailParam.setOutOrderNo(loanApplyEntity.getOutOrderNo());
            DxmLoanRecordDetailDto dxmLoanRecordDetailDto = dxmApi.loanRecordDetail(dxmLoanRecordDetailParam);
            LogUtil.runSensitiveInfoLog("借款结果三方通知：" + JsonUtils.toJson(dxmLoanRecordDetailDto));
            insertLoanTermEntity(dxmLoanRecordDetailDto.getRepayPlanItems(), loanApplyEntity);
            insertLoanRecordEntity(loanApplyEntity, dxmLoanRecordDetailDto.getAnnualRate());
            insertLoanTermRecord(dxmLoanRecordDetailDto.getRepayPlanItems(), loanApplyEntity);
            // 发送借款成功的push通知
            HashMap<String, String> map = null;
            if (!CollectionUtils.isEmpty(dxmLoanRecordDetailDto.getRepayPlanItems())) {
                map = new HashMap<>();
                map.put("dueRepayDate", getDate(dxmLoanRecordDetailDto.getRepayPlanItems().get(0).getShouldRepayDate()));
                map.put("applyNo", loanApplyEntity.getApplyNo());
            }
            pushMessageService.pushMessage(userId, PushMessageTypeEnums.LoanSuccess.getStringValue(), map);
        } else if (DxmConstant.LoanStatus.FAIL.equals(param.getApplyStatus()) || DxmConstant.LoanStatus.REFUSE.equals(param.getApplyStatus())) {
            // 发送借款失败的push通知
            pushMessageService.pushMessage(String.valueOf(loanApplyEntity.getUserId()), PushMessageTypeEnums.LetterFailure.getStringValue(), null);
        }
        //  返回接口结果
        buildResponse(dto, null);
        return dto;
    }

    @Override
    public DxmBaseDto couponNotify(String couponNotifyBody) {
        DxmBaseDto dto = new DxmBaseDto();
        DxmBaseParam notifyParam;
        CouponNotifyParam param;
        try {
            notifyParam = dxmApi.getEncryptDataForNotify(couponNotifyBody);
            LogUtil.runSensitiveInfoLog("优惠券回调通知为：{}", notifyParam);
            param = JsonUtils.parse(String.valueOf(notifyParam.getParams()), CouponNotifyParam.class);
        } catch (BusinessException e) {
            buildResponse(dto, e);
            return dto;
        }
        //查询userid
        LambdaQueryWrapper<LoanUserEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanUserEntity::getSupplier, LoanConstant.Supplier.DXM)
                .eq(LoanUserEntity::getOutOpenId, param.getOpenId())
                .orderByDesc(LoanUserEntity::getCreateTime)
                .last("LIMIT 1");
        LoanUserEntity loanUserEntity = loanUserMapper.selectOne(lambdaQueryWrapper);
        if (loanUserEntity == null) {
            buildResponse(dto, new BusinessException("当前用户未准入，无法发送优惠券"));
            return dto;
        }
        LogUtil.runInfoLog("该用户userId为：{}", SensitiveLog.hideMarkStr(String.valueOf(loanUserEntity.getUserId())));
        //优惠券数据入库
        CouponNotifyEntity couponNotifyEntity = new CouponNotifyEntity();
        couponNotifyEntity.setCouponNo(param.getCouponId());
        couponNotifyEntity.setCouponBatchNo(param.getCouponBatchNo());
        couponNotifyEntity.setUserId(loanUserEntity.getUserId());
        couponNotifyEntity.setSupplier(LoanConstant.Supplier.DXM);
        couponNotifyEntity.setCouponStatus(LoanConstant.LoanCouponStatus.NO_USE);
        getCouponInfo(param, couponNotifyEntity);
        couponNotifyMapper.insert(couponNotifyEntity);
        // 发送优惠券到账的push通知
        Map<String, String> map = new HashMap<>();
        map.put("couponNo", param.getCouponId());
        map.put("couponTitle", param.getCouponTitle());
        pushMessageService.pushMessage(String.valueOf(loanUserEntity.getUserId()), PushMessageTypeEnums.CouponJustArrived.getStringValue(), map);
        buildResponse(dto, null);
        return dto;
    }

    /**
     * todo
     * 1）保存调额调价结果至t_credit_change；
     * 2）发送push或者短信通知用户（参照产品定义）；
     * 3）返回接口结果。
     *
     * @param notifyCreditChangeStatusBody
     * @return
     */
    @Override
    public DxmBaseDto notifyCreditChangeStatus(String notifyCreditChangeStatusBody) {
        DxmBaseDto dto = new DxmBaseDto();
        DxmBaseParam notifyParam;
        NotifyCreditChangeStatusParam param;
        try {
            notifyParam = dxmApi.getEncryptDataForNotify(notifyCreditChangeStatusBody);
            param = JsonUtils.parse(String.valueOf(notifyParam.getParams()), NotifyCreditChangeStatusParam.class);
            ValidationUtils.validate(param);
            LogUtil.runSensitiveInfoLog("notifyCreditChangeStatus: {}", param);
        } catch (BusinessException e) {
            buildResponse(dto, e);
            return dto;
        }

        if (StringUtils.isBlank(param.getApplyNo()) || StringUtils.isBlank(param.getOpenId())) {
            buildResponse(dto, new BusinessException("必传参数为空"));
            return dto;
        }

        Long userId = getUserId(param.getOpenId());
        if (Objects.isNull(userId)) {
            buildResponse(dto, new BusinessException("openid 没有对应的用户准入信息"));
            return dto;
        }
        LogUtil.runInfoLog("该用户userId为：{}", SensitiveLog.hideMarkStr(String.valueOf(userId)));

        LambdaQueryWrapper<CreditApplyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(CreditApplyEntity::getOutOpenId, param.getOpenId())
                .orderByDesc(CreditApplyEntity::getCreateTime).last("LIMIT 1");
        CreditApplyEntity creditApplyEntity = creditApplyMapper.selectOne(lambdaQueryWrapper);

        if (creditApplyEntity == null) {
            buildResponse(dto, new BusinessException("openid 没有对应的荣耀侧授信记录"));
            return dto;
        }
        if (LoanConstant.CreditStatus.APPROVED == creditApplyEntity.getApplyStatus()) {
            userService.deleteEntryInfoCache(Long.toString(creditApplyEntity.getUserId()));
        }
        CreditChangeEntity creditChangeEntity = new CreditChangeEntity();
        creditChangeEntity.setApplyNo(param.getApplyNo());
        creditChangeEntity.setUserId(creditApplyEntity.getUserId());
        creditChangeEntity.setOutOpenId(param.getOpenId());
        creditChangeEntity.setSupplier(creditApplyEntity.getSupplier());
        creditChangeEntity.setOutOrderNo(param.getPartnerApplyNo());
        creditChangeEntity.setLimitChangeType(coverChangeType(param.getLimitChangeType(), DxmConstant.DXMChangeType.DXMLimitChangeType));
        creditChangeEntity.setRateChangeType(coverChangeType(param.getRateChangeType(), DxmConstant.DXMChangeType.DXMRateChangeType));

        if (creditChangeEntity.getLimitChangeType() == null) {
            creditChangeEntity.setLimitChangeType(0);
        }
        if (creditChangeEntity.getRateChangeType() == null) {
            creditChangeEntity.setRateChangeType(0);
        }

        CreditChangeEncryptInfo creditChangeEncryptInfo = new CreditChangeEncryptInfo();
        creditChangeEncryptInfo.setNewLimit(param.getNewLimit());
        creditChangeEncryptInfo.setOldLimit(param.getOldLimit());
        creditChangeEncryptInfo.setRemainLimit(param.getRemainLimit());
        creditChangeEncryptInfo.setTotalLimit(param.getTotalAmount());
        creditChangeEncryptInfo.setNewDayRate(param.getNewDayRate());
        creditChangeEncryptInfo.setOldDayRate(param.getOldDayRate());
        creditChangeEncryptInfo.setNewApr(param.getNewApr());
        creditChangeEncryptInfo.setOldApr(param.getOldApr());
        creditChangeEncryptInfo.setTempDayRate(param.getTempDayRate());
        creditChangeEncryptInfo.setTempApr(param.getTempApr());
        creditChangeEncryptInfo.setTempPriceValidTime(param.getTempPriceDueTime());
        if (Objects.nonNull(param.getTempLimitInfo())) {
            creditChangeEncryptInfo.setTempLimitValidTime(param.getTempLimitInfo().getValidTime());
            creditChangeEncryptInfo.setTempLimitCredit(String.valueOf(param.getTempLimitInfo().getCreditLimit()));
            creditChangeEncryptInfo.setTempLimitAvailable(String.valueOf(param.getTempLimitInfo().getAvailableLimit()));
        }
        if (!Objects.isNull(param.getRepaymentTypeList())
                && creditChangeEntity.getRateChangeType() == LoanConstant.CreditChangeRateChangeType.LOW) {
            DxmRateAdjustInfo rateAdjustInfo = DxmWrapClient.getMinNewDailyRateInfo(param.getRepaymentTypeList().getRateAdjustInfo());
            DxmTmpPriceInfo tmpPriceInfo = DxmWrapClient.getMinTempDayRateInfo(param.getRepaymentTypeList().getTmpPriceInfo());
            if (rateAdjustInfo != null && tmpPriceInfo != null) {
                double tmpDayRate = Double.parseDouble(tmpPriceInfo.getTempDayRate());
                double newDayRate = Double.parseDouble(rateAdjustInfo.getNewDailyRate());
                if (tmpDayRate < newDayRate) {
                    getTmpPriceInfo(creditChangeEncryptInfo, tmpPriceInfo);
                } else {
                    getRateAdjustInfo(creditChangeEncryptInfo, rateAdjustInfo);
                }
            } else if (tmpPriceInfo != null) {
                getTmpPriceInfo(creditChangeEncryptInfo, tmpPriceInfo);
            } else if (rateAdjustInfo != null) {
                getRateAdjustInfo(creditChangeEncryptInfo, rateAdjustInfo);
            }
        }

        creditChangeEntity.setCreateTime(new Date());
        creditChangeEntity.setLimitInfo(EncryptUtil.encrypt(JsonUtils.toJson(creditChangeEncryptInfo)));
        creditChangeMapper.insert(creditChangeEntity);
        //todo 处理push发送
        LoanUserEntity loanUserEntity = getUserInfo(param.getOpenId());
        try {
            Map<String, String> map = new HashMap<>();
            if (creditChangeEntity.getLimitChangeType() != null && creditChangeEntity.getLimitChangeType() == 1) {
                map.put("newLine", getAmount(creditChangeEncryptInfo.getNewLimit()));
                sendPushMessage(Long.toString(loanUserEntity.getUserId()), PushMessageTypeEnums.DrawLine.getStringValue(), map);
                RealTimeReportInfo realTimeReportInfo = new RealTimeReportInfo();
                realTimeReportInfo.setSupplierId(loanUserEntity.getSupplier());
                realTimeReportInfo.setOrderNo(DigestUtils.sha256Hex(creditApplyEntity.getApplyNo()));
                realTimeReportInfo.setUserId(loanUserEntity.getUserId().toString());
                realTimeReportInfo.setType(3);
                realTimeReportInfo.setLimit(getAmount(creditChangeEncryptInfo.getNewLimit() - creditChangeEncryptInfo.getOldLimit()));
                double limitRatio = (double) (creditChangeEncryptInfo.getNewLimit() - creditChangeEncryptInfo.getOldLimit()) / creditChangeEncryptInfo.getOldLimit();
                realTimeReportInfo.setLimitRadio(String.format("%.2f%%", limitRatio * 100));
                reportLog.reportSuccessRealTime(realTimeReportInfo);
            }

            if (creditChangeEntity.getRateChangeType() != null
                    && creditChangeEntity.getRateChangeType() == LoanConstant.CreditChangeRateChangeType.LOW) {
                Map<String, String> rateMap = getMinApr(creditChangeEncryptInfo);
                double dayRate = Double.parseDouble(rateMap.get(LoanConstant.DAY_RATE));
                map.put("dailyInterest", String.valueOf((10000 * dayRate) / 100));
                map.put("annualInterestRate", rateMap.get(LoanConstant.APR) + "%");
                sendPushMessage(Long.toString(loanUserEntity.getUserId()), PushMessageTypeEnums.ReduceInterestRate.getStringValue(), map);
                RealTimeReportInfo realTimeReportInfo = new RealTimeReportInfo();
                realTimeReportInfo.setSupplierId(loanUserEntity.getSupplier());
                realTimeReportInfo.setOrderNo(DigestUtils.sha256Hex(creditApplyEntity.getApplyNo()));
                realTimeReportInfo.setType(4);
                realTimeReportInfo.setUserId(loanUserEntity.getUserId().toString());
                Double oldApr = Double.parseDouble(creditChangeEncryptInfo.getOldApr());
                Double newApr = Double.parseDouble(creditChangeEncryptInfo.getNewApr());
                Double interestRate = oldApr - newApr;
                double interestRateRatio = interestRate / oldApr;
                realTimeReportInfo.setInterestRate(String.format("%.2f%%", interestRate * 100));
                realTimeReportInfo.setInterestRateRatio(String.format("%.2f%%", interestRateRatio * 100));
                reportLog.reportSuccessRealTime(realTimeReportInfo);
            }
        } catch (BusinessException e) {
            LogUtil.runInfoLog(e.toString());
        }
        buildResponse(dto, null);
        return dto;
    }

    /**
     * 获取临价信息
     *
     * @param creditChangeEncryptInfo creditChangeEncryptInfo
     * @param tmpPriceInfo            tmpPriceInfo
     */
    private void getTmpPriceInfo(CreditChangeEncryptInfo creditChangeEncryptInfo, DxmTmpPriceInfo tmpPriceInfo) {
        creditChangeEncryptInfo.setNewApr(tmpPriceInfo.getTempApr());
        creditChangeEncryptInfo.setNewDayRate(tmpPriceInfo.getTempDayRate());
        creditChangeEncryptInfo.setOldDayRate(tmpPriceInfo.getOldDayRate());
        creditChangeEncryptInfo.setOldApr(tmpPriceInfo.getOldApr());
        creditChangeEncryptInfo.setTempDayRate(tmpPriceInfo.getTempDayRate());
        creditChangeEncryptInfo.setTempApr(tmpPriceInfo.getTempApr());
        creditChangeEncryptInfo.setTempPriceValidTime(tmpPriceInfo.getTempPrinceDueTime());
    }

    /**
     * 获取调价信息
     *
     * @param creditChangeEncryptInfo creditChangeEncryptInfo
     * @param rateAdjustInfo          rateAdjustInfo
     */
    private void getRateAdjustInfo(CreditChangeEncryptInfo creditChangeEncryptInfo, DxmRateAdjustInfo rateAdjustInfo) {
        creditChangeEncryptInfo.setNewApr(rateAdjustInfo.getNewAnnualRate());
        creditChangeEncryptInfo.setNewDayRate(rateAdjustInfo.getNewDailyRate());
        creditChangeEncryptInfo.setOldApr(rateAdjustInfo.getOldAnnualRate());
        creditChangeEncryptInfo.setOldDayRate(rateAdjustInfo.getOldDailyRate());
    }


    /**
     * 转换
     *
     * @param changeType
     * @param changeTypeMap
     * @return
     */
    private Integer coverChangeType(String changeType, Map<String, Integer> changeTypeMap) {
        if (StringUtils.isBlank(changeType)) {
            return null;
        }
        return changeTypeMap.get(changeType);
    }

    private void insertLoanTermEntity(List<DxmRepayPlanItems> repayPlanItems, LoanApplyEntity loanApplyEntity) {
        for (DxmRepayPlanItems item : repayPlanItems) {
            LoanTermEntity loanTermEntity = getLoanTermEntity(item);
            loanTermEntity.setUserId(loanApplyEntity.getUserId());
            loanTermEntity.setOutOpenId(loanApplyEntity.getOutOpenId());
            loanTermEntity.setSupplier(loanApplyEntity.getSupplier());
            loanTermEntity.setApplyNo(loanApplyEntity.getApplyNo());
            loanTermEntity.setOutOrderNo(loanApplyEntity.getOutOrderNo());
            loanTermEntity.setTermStatus(covertTermStatus(item.getTermStatus()));
            loanTermEntity.setTermNo(item.getTermNo());
            loanTermEntity.setShouldRepayDate(item.getShouldRepayDate());
            loanTermMapper.insert(loanTermEntity);
        }
    }

    private void insertLoanTermRecord(List<DxmRepayPlanItems> repayPlanItems, LoanApplyEntity loanApplyEntity) {
        for (DxmRepayPlanItems item : repayPlanItems) {
            LoanTermRecordEntity loanTermEntity = getLoanTermRecordEntity(item);
            loanTermEntity.setSupplier(loanApplyEntity.getSupplier());
            loanTermEntity.setApplyNo(loanApplyEntity.getApplyNo());
            loanTermEntity.setOutOrderNo(loanApplyEntity.getOutOrderNo());
            loanTermEntity.setDeviceId(loanApplyEntity.getDeviceId());
            loanTermEntity.setDeviceModel(loanApplyEntity.getDeviceModel());
            loanTermEntity.setCid(loanApplyEntity.getCid());
            loanTermEntity.setSubCid(loanApplyEntity.getSubCid());
            loanTermEntity.setLoc(loanApplyEntity.getLoc());
            loanTermRecordMapper.insert(loanTermEntity);
        }
    }

    private LoanTermEntity getLoanTermEntity(DxmRepayPlanItems item) {
        LoanTermEntity loanTermEntity = new LoanTermEntity();
        loanTermEntity.setTermStatus(covertTermStatus(item.getTermStatus()));
        LoanTermEncryptInfo loanTermEncryptInfo = new LoanTermEncryptInfo();
        loanTermEncryptInfo.setTermAmount(item.getTermAmount());
        loanTermEncryptInfo.setTermPrincipal(item.getTermPrincipal());
        loanTermEncryptInfo.setTermInterest(item.getTermInterest());
        loanTermEncryptInfo.setTermPenalty(item.getTermPenalty());
        loanTermEncryptInfo.setTermServiceFee(item.getTermServiceFee());
        loanTermEncryptInfo.setTermOverdueFee(item.getTermOverdueFee());
        loanTermEncryptInfo.setTermViolateFee(item.getTermViolateFee());
        loanTermEncryptInfo.setPayableTermAmount(item.getPayableTermAmount());
        loanTermEncryptInfo.setPayableTermPrincipal(item.getPayableTermPrincipal());
        loanTermEncryptInfo.setPayableTermInterest(item.getPayableTermInterest());
        loanTermEncryptInfo.setPayableTermPenalty(item.getPayableTermPrinPenalty());
        loanTermEncryptInfo.setPayableTermServiceFee(item.getPayableTermServiceFee());
        loanTermEncryptInfo.setPayableTermOverdueFee(item.getPayableTermOverdueFee());
        loanTermEncryptInfo.setPayableTermViolateFee(item.getPayableViolateFee());
        loanTermEncryptInfo.setTermServiceFee(item.getTermServiceFee());
        loanTermEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanTermEncryptInfo)));
        return loanTermEntity;
    }

    private LoanTermRecordEntity getLoanTermRecordEntity(DxmRepayPlanItems item) {
        LoanTermRecordEntity entity = new LoanTermRecordEntity();
        entity.setTermStatus(covertTermStatus(item.getTermStatus()));
        entity.setTermNo(item.getTermNo());
        entity.setShouldRepayDate(item.getShouldRepayDate());
        entity.setTermAmount(item.getTermAmount());
        entity.setTermPrincipal(item.getTermPrincipal());
        entity.setTermInterest(item.getTermInterest());
        entity.setTermPenalty(item.getTermPenalty());
        entity.setTermServiceFee(item.getTermServiceFee());
        entity.setTermOverdueFee(item.getTermOverdueFee());
        entity.setTermViolateFee(item.getTermViolateFee());
        entity.setTermReductionAmount(item.getTermReductionAmount());
        entity.setPaidTermAmount(item.getPaidTermAmount());
        entity.setPaidTermPrincipal(item.getPaidTermPrincipal());
        entity.setPaidTermInterest(item.getPaidTermInterest());
        entity.setPaidTermPenalty(item.getPaidTermInterPenalty());
        entity.setPaidTermServiceFee(item.getPaidTermServiceFee());
        entity.setPaidTermOverdueFee(item.getPaidTermOverdueFee());
        entity.setPaidTermViolateFee(item.getPaidViolateFee());
        entity.setPaidTermReductionAmount(item.getPaidTermReductionAmount());
        entity.setPayableTermAmount(item.getPayableTermAmount());
        entity.setPayableTermPrincipal(item.getPayableTermPrincipal());
        entity.setPayableTermInterest(item.getPayableTermInterest());
        entity.setPayableTermPenalty(item.getPayableTermPrinPenalty());
        entity.setPayableTermServiceFee(item.getPayableTermServiceFee());
        entity.setPayableTermOverdueFee(item.getPayableTermOverdueFee());
        entity.setPayableTermViolateFee(item.getPayableViolateFee());
        entity.setPaidTime(item.getPaidTime());
        return entity;
    }

    private void insertLoanRecordEntity(LoanApplyEntity loanApplyEntity, String apr) {
        LoanRecordEntity loanRecordEntity = new LoanRecordEntity();
        BeanUtils.copyProperties(loanApplyEntity, loanRecordEntity);
        LoanApplyEncryptInfo loanApplyEncryptInfo = getLoanApplyEncryptInfo(loanApplyEntity);
        BeanUtils.copyProperties(loanApplyEncryptInfo, loanRecordEntity);
        loanRecordEntity.setApr(apr);

        loanRecordService.saveOrUpdate(loanRecordEntity, new LambdaUpdateWrapper<LoanRecordEntity>()
                .eq(LoanRecordEntity::getApplyNo, loanRecordEntity.getApplyNo())
                .eq(LoanRecordEntity::getOutOrderNo, loanRecordEntity.getOutOrderNo()));
    }

    private LoanApplyEntity updateLoanApplyEntity(LoanApplyEntity entity, DxmLoanResultNotifyParam param) {
        Integer status = DxmWrapClient.covertLoanStatus(param.getApplyStatus(), false);
        if (status != null) {
            entity.setApplyStatus(status);
            if (status.equals(LoanConstant.LoanStatus.SUCCESS)) {
                entity.setRepayStatus(LoanConstant.LoanTermStatus.REND_REPAY);
            }
        }
        LoanApplyEncryptInfo loanApplyEncryptInfo = getLoanApplyEncryptInfo(entity);
        loanApplyEncryptInfo.setLoanTime(param.getLoanTime());
        loanApplyEncryptInfo.setEffectiveDate(param.getEffectiveDate());
        Integer repayMethod = DxmWrapClient.covertRepayMethod(param.getRepayMethod());
        if (!Objects.isNull(repayMethod)){
            loanApplyEncryptInfo.setRepayMethod(DxmWrapClient.covertRepayMethod(param.getRepayMethod()));
        }
        loanApplyEncryptInfo.setDayRate(param.getDayRate());
        entity.setRefuseCode(param.getRefuseCode());
        entity.setRefuseMsg(param.getRefuseMsgData());
        if (StringUtils.isNotBlank(param.getOutOrderNo())){
            entity.setOutOrderNo(param.getOutOrderNo());
        }
        if (StringUtils.isNotBlank(param.getLoanSource())){
            entity.setLoanSource(param.getLoanSource());
        }
        if (param.getLoanInfo() != null) {
            if (!Objects.isNull(param.getLoanInfo().getLoanAmount())) {
                loanApplyEncryptInfo.setLoanAmount(param.getLoanInfo().getLoanAmount());
            }
            if (!Objects.isNull(param.getLoanInfo().getTotalTerm())) {
                loanApplyEncryptInfo.setTotalTerm(param.getLoanInfo().getTotalTerm());
            }
            if (!Objects.isNull(param.getLoanInfo().getLoanUse())) {
                loanApplyEncryptInfo.setLoanUse(param.getLoanInfo().getLoanUse());
            }
            entity.setApplyTime(getLongTime(param.getLoanInfo().getApplyTime()));
        }
        entity.setLoanInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanApplyEncryptInfo)));
        Integer identify = DxmWrapClient.covertIdentify(param.getIdentity());
        entity.setIdentity(identify != null ? identify : entity.getIdentity());
        entity.setInstitutionName(param.getInstitutionNames() != null ? param.getInstitutionNames(): entity.getInstitutionName());

        loanApplyMapper.update(null, new LambdaUpdateWrapper<LoanApplyEntity>().eq(LoanApplyEntity::getOutOrderNo, entity.getOutOrderNo())
                .set(LoanApplyEntity::getApplyStatus, entity.getApplyStatus())
                .set(LoanApplyEntity::getRepayStatus, entity.getRepayStatus())
                .set(LoanApplyEntity::getRefuseCode, entity.getRefuseCode())
                .set(LoanApplyEntity::getRefuseMsg, entity.getRefuseMsg())
                .set(LoanApplyEntity::getLoanSource, entity.getLoanSource())
                .set(LoanApplyEntity::getApplyTime, entity.getApplyTime())
                .set(LoanApplyEntity::getLoanInfo, entity.getLoanInfo())
                .set(LoanApplyEntity::getIdentity, entity.getIdentity())
                .set(LoanApplyEntity::getInstitutionName, entity.getInstitutionName())
        );
        return entity;
    }

    /**
     * 将"yyyy-MM-dd HH:mm:ss"转换成Long类型时间戳
     */
    private Long getLongTime(String time) {
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = sdf.parse(time);
        } catch (Exception e) {
            LogUtil.runErrorLog("时间转换出错,{}", time);
            throw new BusinessException("时间转换出错");
        }
        return date.getTime();
    }

    /**
     * 借款的期次状态转换
     */
    private Integer covertTermStatus(String status) {
        if (status == null) {
            return null;
        }
        if (status.equals(DxmConstant.LoanTermStatus.PAYOFF)) {
            return LoanConstant.LoanTermStatus.SETTLE;
        } else if (status.equals(DxmConstant.LoanTermStatus.OVERDUE)) {
            return LoanConstant.LoanTermStatus.OVERDUE;
        } else if (status.equals(DxmConstant.LoanTermStatus.PAYING)) {
            return LoanConstant.LoanTermStatus.REND_REPAY;
        } else if (status.equals(DxmConstant.LoanTermStatus.PART_PAID)) {
            return LoanConstant.LoanTermStatus.PART_REPAY;
        } else if (status.equals(DxmConstant.LoanTermStatus.GRACE)) {
            return LoanConstant.LoanTermStatus.GRACE_PERIOD;
        }
        return null;
    }


    /**
     * 返回体构造
     *
     * @param dto dto
     * @param e   e
     */
    private void buildResponse(DxmBaseDto dto, BusinessException e) {
        if (e == null) {
            dto.setData(DxmConstant.CallBackDataCode.SUCCESS);
            dto.setSuccess(true);
        } else {
            dto.setData(DxmConstant.CallBackDataCode.FAIL);
            dto.setSuccess(false);
            dto.setDesc(e.getErrorMessage());
        }
    }

    /**
     * 授信状态转换
     *
     * @param applyStatus applyStatus
     * @return Integer
     */
    private static Integer covertApplyResult(String applyStatus) {
        if (StringUtils.equals(applyStatus, DxmConstant.CreditApplyResult.AUDITING)) {
            return LoanConstant.CreditStatus.UNDER_REVIEW;
        } else if (StringUtils.equals(applyStatus, DxmConstant.CreditApplyResult.PASS)) {
            return LoanConstant.CreditStatus.APPROVED;
        } else if (StringUtils.equals(applyStatus, DxmConstant.CreditApplyResult.REFUSE)) {
            return LoanConstant.CreditStatus.REJECT;
        } else if (StringUtils.equals(applyStatus, DxmConstant.CreditApplyResult.WAIT)) {
            return LoanConstant.CreditStatus.UNDER_REVIEW;
        }
        return LoanConstant.CreditStatus.UNDER_REVIEW;
    }


    /**
     * 查询后的数据库操作处理
     *
     * @param queryRepayResultDto queryRepayResultDto
     */
    private void updateRepayResult(com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto queryRepayResultDto,
                                   DxmRepayResultNotifyParam param) {
        LoanUserEntity loanUserEntity = getUserInfo(param.getOpenId());
        // 为空要生成
        if (StringUtils.isBlank(param.getTransNo())) {
            queryRepayResultDto.setTransNo(IdUtils.generateRepayOrderId(Long.toString(loanUserEntity.getUserId()), LoanConstant.Supplier.DXM));
            param.setTransNo(queryRepayResultDto.getTransNo());
        }

        if (loanUserEntity != null) {
            MDC.put(CommonConstant.MDC_USERID, loanUserEntity.getUserId().toString());
            MDC.put(CommonConstant.MDC_DEVICEID, loanUserEntity.getDeviceId());
        }
        String outOrderNo = queryRepayResultDto.getRepayResultList().get(0).getOutOrderNo();
        LambdaQueryWrapper<LoanRepayEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanRepayEntity::getRepayNo, queryRepayResultDto.getTransNo());
        lambdaQueryWrapper.eq(LoanRepayEntity::getUserId, loanUserEntity.getUserId());
        LoanRepayEntity loanRepayEntity = loanRepayMapper.selectOne(lambdaQueryWrapper);
        LambdaQueryWrapper<LoanApplyEntity> loanApplyWrapper = new LambdaQueryWrapper<>();
        loanApplyWrapper.eq(LoanApplyEntity::getOutOrderNo, outOrderNo)
                .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId()).or().eq(LoanApplyEntity::getApplyNo, outOrderNo)
                .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
        LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(loanApplyWrapper);
        reportRepayStatus(queryRepayResultDto, loanUserEntity, loanApplyEntity);
        if (loanRepayEntity == null && StringUtils.equals(param.getRepaySource(), "OTHER")) {
            loanRepayEntity = new LoanRepayEntity();
            loanRepayEntity.setRepayNo(queryRepayResultDto.getTransNo());
            loanRepayEntity.setUserId(loanUserEntity.getUserId());
            loanRepayEntity.setSupplier(LoanConstant.Supplier.DXM);
            loanRepayEntity.setOutLoanNo(param.getOutOrderNo());
            loanRepayEntity.setOutOrderNo("");
            loanRepayEntity.setOutOpenId(loanUserEntity.getOutOpenId());
            loanRepayEntity.setRepayType(covertRepayType(queryRepayResultDto.getRepayType()));
            loanRepayEntity.setRepayStatus(queryRepayResultDto.getRepayStatus());
            loanRepayEntity.setRepayResult(queryRepayResultDto.getRepayResult());
            loanRepayEntity.setRepaySource(param.getRepaySource());
            loanRepayEntity.setRepayOriginType(param.getRepayOriginType());
            loanRepayEntity.setDeviceModel(loanUserEntity.getDeviceModel() == null ? "" : loanUserEntity.getDeviceModel());
            loanRepayEntity.setDeviceId(loanUserEntity.getDeviceId() == null ? "" : loanUserEntity.getDeviceId());
            LoanRepayEncryptInfo loanRepayEncryptInfo = new LoanRepayEncryptInfo();
            loanRepayEncryptInfo.setCouponAmount(queryRepayResultDto.getCouponAmount());
            loanRepayEncryptInfo.setRepayAmount(queryRepayResultDto.getRepayAmount());
            loanRepayEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanRepayEncryptInfo)));
            loanRepayMapper.insert(loanRepayEntity);
            LoanRepayRecordEntity loanRepayRecordEntity = new LoanRepayRecordEntity();
            BeanUtils.copyProperties(loanRepayEntity, loanRepayRecordEntity);
            loanRepayRecordMapper.insert(loanRepayRecordEntity);
        } else if (loanRepayEntity != null && (!Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, loanRepayEntity.getRepayStatus())
                && !Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, loanRepayEntity.getRepayStatus()))) {
            LoanRepayEntity updateRepayEntity = new LoanRepayEntity();
            if (StringUtils.isBlank(param.getOutOrderNo())) {
                updateRepayEntity.setOutLoanNo(queryRepayResultDto.getRepayResultList().get(0).getOutOrderNo());
            }
            updateRepayEntity.setRepayResult(queryRepayResultDto.getRepayResult());
            updateRepayEntity.setRepayStatus(queryRepayResultDto.getRepayStatus());
            updateRepayEntity.setRepayOriginType(queryRepayResultDto.getRepayOriginType());
            updateRepayEntity.setRepaySource(param.getRepaySource());
            updateRepayEntity.setRepayNo(loanRepayEntity.getRepayNo());
            loanRepayMapper.updateById(updateRepayEntity);
            LoanRepayRecordEntity loanRepayRecordEntity = new LoanRepayRecordEntity();
            BeanUtils.copyProperties(updateRepayEntity, loanRepayRecordEntity);
            LambdaUpdateWrapper<LoanRepayRecordEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(LoanRepayRecordEntity::getRepayNo, queryRepayResultDto.getTransNo());
            loanRepayRecordMapper.update(loanRepayRecordEntity, lambdaUpdateWrapper);
        }

        List<RepayResultList> repayResultList = queryRepayResultDto.getRepayResultList();
        for (RepayResultList result : repayResultList) {
            LambdaQueryWrapper<LoanRepayResultEntity> resultWrapper = new LambdaQueryWrapper<>();
            resultWrapper.eq(LoanRepayResultEntity::getRepayNo, queryRepayResultDto.getTransNo())
                    .eq(LoanRepayResultEntity::getOutOrderNo, result.getOutOrderNo())
                    .orderByDesc(LoanRepayResultEntity::getCreateTime).last("LIMIT 1");
            LoanRepayResultEntity loanRepayResultEntity = loanRepayResultMapper.selectOne(resultWrapper);
            LoanRepayResultEncryptInfo info = new LoanRepayResultEncryptInfo();
            info.setRepayAmount(result.getAmount());
            info.setReductionAmount(result.getReductionAmount());
            if (loanRepayResultEntity == null) {
                loanRepayResultEntity = new LoanRepayResultEntity();
                loanRepayResultEntity.setRepayStatus(result.getStatus());
                loanRepayResultEntity.setUserId(loanUserEntity.getUserId());
                loanRepayResultEntity.setRepayNo(param.getTransNo());
                loanRepayResultEntity.setOutOpenId(loanUserEntity.getOutOpenId());
                loanRepayResultEntity.setSupplier(loanUserEntity.getSupplier());
                loanRepayResultEntity.setOutOrderNo(result.getOutOrderNo());
                loanRepayResultEntity.setCid(loanApplyEntity.getCid());
                loanRepayResultEntity.setLoc(loanApplyEntity.getLoc());
                loanRepayResultEntity.setSubCid(loanApplyEntity.getSubCid());
                loanRepayResultEntity.setDeviceId(loanApplyEntity.getDeviceId());
                loanRepayResultEntity.setDeviceModel(loanApplyEntity.getDeviceModel());
                LoanRepayResultEncryptInfo resultEncryptInfo = new LoanRepayResultEncryptInfo();
                resultEncryptInfo.setRepayAmount(result.getAmount());
                resultEncryptInfo.setReductionAmount(result.getReductionAmount());
                loanRepayResultEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(resultEncryptInfo)));
                loanRepayResultMapper.insert(loanRepayResultEntity);
            }

            if (loanRepayResultEntity != null) {
                LoanRepayResultEntity repayResultEntity = new LoanRepayResultEntity();
                repayResultEntity.setRecordId(loanRepayResultEntity.getRecordId());
                repayResultEntity.setRepayStatus(result.getStatus());
                LoanRepayResultEncryptInfo resultEncryptInfo = new LoanRepayResultEncryptInfo();
                resultEncryptInfo.setRepayAmount(result.getAmount());
                resultEncryptInfo.setReductionAmount(result.getReductionAmount());
                repayResultEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(resultEncryptInfo)));
                loanRepayResultMapper.updateById(repayResultEntity);
            }
            List<RepayItems> repayItems = result.getRepayItems();
            updateTermInfo(repayItems, result, param, loanUserEntity, loanApplyEntity);
        }
    }

    /**
     * 上报还款状态
     *
     * @param queryRepayResultDto queryRepayResultDto
     * @param loanUserEntity      loanUserEntity
     */
    private void reportRepayStatus(QueryRepayResultDto queryRepayResultDto, LoanUserEntity loanUserEntity, LoanApplyEntity loanApplyEntity) {
        if (Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, queryRepayResultDto.getRepayStatus())
                || Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, queryRepayResultDto.getRepayStatus())) {
            reportLog.reportSuccessForLoan(ReportEvent.LOAN_REPAY_SUCCESS, LoanConstant.Supplier.DXM, null, RequestUtils.getTraceId(),
                    String.valueOf(loanApplyEntity.getUserId()), loanApplyEntity.getDeviceId(), loanApplyEntity.getDeviceModel());
        } else if (Objects.equals(LoanConstant.RepayStatus.REPAY_FAIL, queryRepayResultDto.getRepayStatus())) {
            reportLog.reportFailForLoan(ReportEvent.LOAN_REPAY_FAIL, LoanConstant.Supplier.DXM,
                RequestUtils.getTraceId(), null, queryRepayResultDto.getRepayResult(),
                    null, String.valueOf(loanUserEntity.getUserId()), loanUserEntity.getDeviceId(), loanUserEntity.getDeviceModel());
        }
    }

    /**
     * 期次还款记录处理
     *
     * @param repayItems  repayItems
     * @param repayResult repayResult
     * @param param       param
     */
    private void updateTermInfo(List<RepayItems> repayItems, RepayResultList repayResult,
                                DxmRepayResultNotifyParam param, LoanUserEntity loanUserEntity, LoanApplyEntity loanApplyEntity) {
        if (CollectionUtils.isEmpty(repayItems) || (LoanConstant.RepayStatus.REPAY_SUCCESS != repayResult.getStatus()
                && LoanConstant.RepayStatus.REPAY_PART_SUCCESS != repayResult.getStatus())) {
            return;
        }
        for (RepayItems item : repayItems) {
            LambdaUpdateWrapper<LoanRepayTermEntity> resultWrapper = new LambdaUpdateWrapper<>();
            resultWrapper.eq(LoanRepayTermEntity::getRepayNo, param.getTransNo())
                    .eq(LoanRepayTermEntity::getOutOrderNo, repayResult.getOutOrderNo())
                    .eq(LoanRepayTermEntity::getTermNo, item.getTermNo());
            LoanRepayTermEntity loanRepayTermEntity = new LoanRepayTermEntity();
            boolean exist = loanRepayTermMapper.exists(resultWrapper);
            if (exist) {
                return;
            }
            loanRepayTermEntity.setRepayNo(param.getTransNo());
            loanRepayTermEntity.setTermNo(item.getTermNo());
            loanRepayTermEntity.setUserId(loanUserEntity.getUserId());
            loanRepayTermEntity.setOutOpenId(loanUserEntity.getOutOpenId());
            loanRepayTermEntity.setSupplier(loanUserEntity.getSupplier());
            loanRepayTermEntity.setCid(loanApplyEntity.getCid());
            loanRepayTermEntity.setSubCid(loanApplyEntity.getSubCid());
            loanRepayTermEntity.setLoc(loanApplyEntity.getLoc());
            loanRepayTermEntity.setOutOrderNo(repayResult.getOutOrderNo());
            loanRepayTermEntity.setDeviceId(loanApplyEntity.getDeviceId());
            loanRepayTermEntity.setDeviceModel(loanApplyEntity.getDeviceModel());
            LoanRepayTermEncryptInfo loanRepayTermEncryptInfo = new LoanRepayTermEncryptInfo();
            loanRepayTermEncryptInfo.setTermAmount(item.getTermAmount());
            loanRepayTermEncryptInfo.setTermPrincipal(item.getTermPrincipal());
            loanRepayTermEncryptInfo.setTermInterest(item.getTermInterest());
            loanRepayTermEncryptInfo.setTermPrinPenalty(item.getTermPrinPenalty());
            loanRepayTermEncryptInfo.setTermInterPenalty(item.getTermInterPenalty());
            loanRepayTermEncryptInfo.setTermPenalty(item.getTermInterPenalty() + item.getTermPrinPenalty());
            loanRepayTermEncryptInfo.setTermOverdueFee(item.getTermOverdue());
            loanRepayTermEncryptInfo.setTermServiceFee(item.getTermFee());
            if (item.getTermDiscount() != null) {
                loanRepayTermEncryptInfo.setTermDiscount(item.getTermDiscount());
            }
            if (item.getTermViolateFee() != null) {
                loanRepayTermEncryptInfo.setTermViolateFee(item.getTermViolateFee());
            }
            loanRepayTermEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanRepayTermEncryptInfo)));
            loanRepayTermMapper.insert(loanRepayTermEntity);
            LoanRepayTermRecordEntity loanRepayTermRecordEntity = new LoanRepayTermRecordEntity();
            BeanUtils.copyProperties(loanRepayTermEntity, loanRepayTermRecordEntity);
            BeanUtils.copyProperties(loanRepayTermEncryptInfo, loanRepayTermRecordEntity);
            loanRepayTermRecordMapper.insert(loanRepayTermRecordEntity);
        }
    }

    /**
     * 获取userid
     *
     * @param openId openId
     * @return Long
     */
    private Long getUserId(String openId) {
        LambdaQueryWrapper<LoanUserEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanUserEntity::getSupplier, LoanConstant.Supplier.DXM)
                .eq(LoanUserEntity::getOutOpenId, openId)
                .eq(LoanUserEntity::getAccessResult, LoanConstant.UserAccessResult.APPROVE)
                .orderByDesc(LoanUserEntity::getCreateTime).last("LIMIT 1");

        LoanUserEntity loanUserEntity = loanUserMapper.selectOne(lambdaQueryWrapper);
        if (loanUserEntity == null) {
            return null;
        }
        return loanUserEntity.getUserId();
    }

    /**
     * 获取userid
     *
     * @param openId openId
     * @return Long
     */
    private LoanUserEntity getUserInfo(String openId) {
        LambdaQueryWrapper<LoanUserEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanUserEntity::getSupplier, LoanConstant.Supplier.DXM)
                .eq(LoanUserEntity::getOutOpenId, openId)
                .eq(LoanUserEntity::getAccessResult, LoanConstant.UserAccessResult.APPROVE)
                .orderByDesc(LoanUserEntity::getCreateTime).last("LIMIT 1");

        LoanUserEntity loanUserEntity = loanUserMapper.selectOne(lambdaQueryWrapper);
        return loanUserEntity;
    }

    /**
     * 还款类型
     *
     * @param repayType repayType
     * @return
     */
    private Integer covertRepayType(String repayType) {
        if (StringUtils.equals("PRE", repayType)) {
            return LoanConstant.RepayType.PRE;
        }
        return LoanConstant.RepayType.NORMAL;
    }

    /**
     * 还款push Type 转换
     *
     * @param detailDtoList detailDtoList
     * @param param         param
     */
    private void covertRepayPushTye(List<DxmLoanRecordDetailDto> detailDtoList, DxmRepayResultNotifyParam param) {
        if (CollectionUtils.isEmpty(detailDtoList)) {
            return;
        }
        DxmLoanRecordDetailDto dto = detailDtoList.get(0);
        String type;
        LogUtil.runInfoLog("开始 还款push处理");
        if ((StringUtils.equals("OTHER", param.getRepaySource()) && Objects.isNull(param.getRepayOriginType())) ||
                (Objects.nonNull(param.getRepayOriginType()) && param.getRepayOriginType() == 1)) {
            if ((DxmWrapClient.covertRepayStatus(param.getRepayStatus()) == LoanConstant.RepayStatus.REPAY_SUCCESS
                    || DxmWrapClient.covertRepayStatus(param.getRepayStatus()) == LoanConstant.RepayStatus.REPAY_PART_SUCCESS)
                    && "PAYOFF".equals(dto.getStatus())) {
                type = PushMessageTypeEnums.SuccessfulWithHolding.getStringValue();
                sendRepayPushMessage(type, detailDtoList, param);
            } else if (LoanConstant.RepayStatus.REPAY_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())
                    || LoanConstant.RepayStatus.REPAY_PART_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())) {
                type = PushMessageTypeEnums.SuccessfulWithHolding.getStringValue();
            } else {
                type = PushMessageTypeEnums.WithHoldingFailure.getStringValue();
            }
        } else {
            if (LoanConstant.RepayStatus.REPAY_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())
                    || LoanConstant.RepayStatus.REPAY_PART_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())) {
                type = PushMessageTypeEnums.RepaymentSuccess.getStringValue();
            } else {
                type = PushMessageTypeEnums.RepaymentFailure.getStringValue();
            }
        }

        if ("PAYOFF".equals(dto.getStatus())) {
            type = PushMessageTypeEnums.SuccessfulSettlement.getStringValue();
        }
        sendRepayPushMessage(type, detailDtoList, param);
    }


    /**
     * 还款发送push 参数拼装
     *
     * @param type          type
     * @param detailDtoList detailDtoList
     * @param param         param
     */
    private void sendRepayPushMessage(String type, List<DxmLoanRecordDetailDto> detailDtoList, DxmRepayResultNotifyParam param) {
        DxmLoanRecordDetailDto dto = detailDtoList.get(0);
        Long balanceAmount = dto.getLoanAmount() - dto.getPaidPrinAmount();
        String repaymentAmountKey = "repaymentAmount";
        String principalBalanceKey = "principalBalance";
        String settleAmountKey = "Amount";
        String limit = "limit";
        String repayNo = "repayNo";
        String repayAmount = getAmount(param.getRepayAmount());
        String principalBalance = getAmount(balanceAmount);
        Map<String, String> map = new HashMap<>();
        String redisKey = type + getUserId(param.getOpenId()).toString() + param.getOutOrderNo() + param.getTransNo();
        if (redisUtil.keyExists(redisKey)) {
            LogUtil.runInfoLog("同一订单号的还款回调重复发送");
            return;
        } else {
            redisUtil.setForDays(redisKey, type, 1);
        }
        if (StringUtils.equals(PushMessageTypeEnums.RepaymentSuccess.getStringValue(), type)) {
            map.put(repaymentAmountKey, repayAmount);
            map.put(principalBalanceKey, principalBalance);
        } else if (StringUtils.equals(PushMessageTypeEnums.SuccessfulSettlement.getStringValue(), type)) {
            map.put(settleAmountKey, getAmount(dto.getLoanAmount()));
            QueryUserCreditInfoParam queryUserCreditInfoParam = new QueryUserCreditInfoParam();
            queryUserCreditInfoParam.setUserId(param.getUserId());
            queryUserCreditInfoParam.setSupplier(LoanConstant.Supplier.DXM);
            queryUserCreditInfoParam.setOpenId(param.getOpenId());
            QueryUserCreditInfoDto queryUserCreditInfoDto = loanClient.queryUserCreditInfo(queryUserCreditInfoParam);
            if (queryUserCreditInfoDto != null) {
                map.put(limit, getAmount(queryUserCreditInfoDto.getRemainLimit()));
                if (!Objects.isNull(queryUserCreditInfoDto.getTotalAvailableLimit())) {
                    map.put(limit, getAmount(queryUserCreditInfoDto.getTotalAvailableLimit()));
                }
            }
        } else if (StringUtils.equals(PushMessageTypeEnums.SuccessfulWithHolding.getStringValue(), type)) {
            map.put("repayAmount", repayAmount);
        } else if (StringUtils.equals(PushMessageTypeEnums.WithHoldingFailure.getStringValue(), type)) {
            redisKey = type + getUserId(param.getOpenId()).toString() + param.getOutOrderNo();
            if (redisUtil.keyExists(redisKey)) {
                return;
            } else {
                redisUtil.setForDays(redisKey, type, 1);
            }
            map.put("repayAmount", repayAmount);
        } else if (StringUtils.equals(PushMessageTypeEnums.RepaymentFailure.getStringValue(), type)) {
            map.put(repayNo, param.getOutOrderNo());
        }
        sendPushMessage(getUserId(param.getOpenId()).toString(), type, map);
    }

    /**
     * 发送psuh信息
     *
     * @param userId userId
     * @param type   type
     * @param map    map
     */
    private void sendPushMessage(String userId, String type, Map<String, String> map) {
        try {
            pushMessageService.pushMessage(userId, type, map);
        } catch (BusinessException e) {
            LogUtil.runErrorLog("小贷发送 push信息失败 {}, {}", type, e);
        }
    }

    public LoanApplyEncryptInfo getLoanApplyEncryptInfo(LoanApplyEntity loanApplyEntity) {
        String decryptLoanInfo = EncryptUtil.decrypt(loanApplyEntity.getLoanInfo());
        LoanApplyEncryptInfo loanApplyEncryptInfo = null;
        if (!StringUtils.isBlank(decryptLoanInfo)) {
            loanApplyEncryptInfo = JsonUtils.parse(decryptLoanInfo, new TypeReference<LoanApplyEncryptInfo>() {
            });
        } else {
            loanApplyEncryptInfo = new LoanApplyEncryptInfo();
        }
        return loanApplyEncryptInfo;
    }

    /**
     * 金额转换
     *
     * @param amount amount
     * @return getAmount
     */
    private String getAmount(Long amount) {
        double result = (double) amount / 100;
        return String.format("%.2f", result);
    }

    /**
     * 日期转换
     */
    private String getDate(String shouldRepayDate) {
        String year = shouldRepayDate.substring(0, 4);
        String month = shouldRepayDate.substring(4, 6);
        String day = shouldRepayDate.substring(6, 8);
        return year + "年" + month + "月" + day + "日";
    }

    /***
     * 获取利率
     *
     * @param userId
     * @param openId
     * @param supplier
     * @return
     */
    private Map<String, String> getApr(String userId, String openId, Integer supplier, Map<String, String> rateMap) {
        QueryUserCreditInfoParam creditInfoParam = new QueryUserCreditInfoParam();
        creditInfoParam.setOpenId(openId);
        creditInfoParam.setUserId(userId);
        creditInfoParam.setSupplier(supplier);
        QueryUserCreditInfoDto queryUserCreditInfoDto = loanClient.queryUserCreditInfo(creditInfoParam);
        if (rateMap != null) {
            LogUtil.runInfoLog("getMinRate结果为:{}", getMinRate(queryUserCreditInfoDto).toString());
            rateMap.putAll(getMinRate(queryUserCreditInfoDto));
        }
        return getMinApr(queryUserCreditInfoDto);
    }

    private Map<String, String> getMinApr(QueryUserCreditInfoDto queryUserCreditInfoDto) {
        if (queryUserCreditInfoDto != null && queryUserCreditInfoDto.getProductInfo() != null) {
            LocalDateTime currentTime = LocalDateTime.now();
            //找到所有apr和tempApr中的最小值
            BigDecimal aprNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .flatMap(productInfo -> {
                        if (isTempPriceDueTimeExpired(productInfo.getTempPriceDueTime(), currentTime)) {
                            return Stream.of(productInfo.getApr());
                        } else {
                            return Stream.of(productInfo.getApr(), productInfo.getTempApr());
                        }
                    })
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String apr = aprNum == null ? null : String.valueOf(aprNum);
            //找到所有dayRate和tempDayRate中的最小值
            BigDecimal dayRateNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .flatMap(productInfo -> {
                        if (isTempPriceDueTimeExpired(productInfo.getTempPriceDueTime(), currentTime)) {
                            return Stream.of(productInfo.getDayRate());
                        } else {
                            return Stream.of(productInfo.getDayRate(), productInfo.getTempDayRate());
                        }
                    })
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String dayRate = dayRateNum == null ? null : String.valueOf(dayRateNum);
            HashMap<String, String> rateMap = new HashMap<>();
            rateMap.put(LoanConstant.APR, apr);
            rateMap.put(LoanConstant.DAY_RATE, dayRate);
            return rateMap;
        }
        //查询不到不返回
        return new HashMap<>();
    }

    private Map<String, String> getMinRate(QueryUserCreditInfoDto queryUserCreditInfoDto) {
        if (queryUserCreditInfoDto != null && queryUserCreditInfoDto.getProductInfo() != null) {

            //找到所有apr中的最小值
            BigDecimal aprNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .map(productInfo -> Arrays.asList(productInfo.getApr()))
                    .flatMap(Collection::stream)
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String apr = aprNum == null ? null : String.valueOf(aprNum);
            //找到所有tempApr中的最小值
            BigDecimal tempAprNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .map(productInfo -> Arrays.asList(productInfo.getTempApr()))
                    .flatMap(Collection::stream)
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String tempApr = tempAprNum == null ? null : String.valueOf(tempAprNum);
            //找到所有dayRate中的最小值
            BigDecimal dayRateNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .map(productInfo -> Arrays.asList(productInfo.getDayRate()))
                    .flatMap(Collection::stream)
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String dayRate = dayRateNum == null ? null : String.valueOf(dayRateNum);
            //找到所有tempDayRate中的最小值
            BigDecimal tempDayRateNum = queryUserCreditInfoDto.getProductInfo().stream()
                    .map(productInfo -> Arrays.asList(productInfo.getTempDayRate()))
                    .flatMap(Collection::stream)
                    .filter(StringUtils::isNotBlank)
                    .map(this::toBigDecimal)
                    .map(Optional::get)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            String tempDayRate = tempDayRateNum == null ? null : String.valueOf(tempDayRateNum);
            HashMap<String, String> rateMap = new HashMap<>();
            rateMap.put(LoanConstant.APR, apr);
            rateMap.put(LoanConstant.DAY_RATE, dayRate);
            rateMap.put(LoanConstant.TEMP_APR, tempApr);
            rateMap.put(LoanConstant.TEMP_DAY_RATE, tempDayRate);
            return rateMap;
        }
        //查询不到不返回
        return new HashMap<>();
    }

    private Optional<BigDecimal> toBigDecimal(String rate) {
        try {
            return Optional.of(new BigDecimal(rate));
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }

    /**
     * 最低利率
     *
     * @param param
     * @return
     */
    private Map<String, String> getMinApr(CreditChangeEncryptInfo param) {
        HashMap<String, String> rateMap = new HashMap<>();
        rateMap.put(LoanConstant.APR, compareAndReturnSmaller(param.getNewApr(), param.getTempApr()));
        rateMap.put(LoanConstant.DAY_RATE, compareAndReturnSmaller(param.getNewDayRate(), param.getTempDayRate()));
        return rateMap;
    }

    private boolean isTempPriceDueTimeExpired(String tempPriceDueTime, LocalDateTime currentTime) {
        if (StringUtils.isBlank(tempPriceDueTime)) {
            return true;
        }
        try {
            LocalDateTime dueTime = LocalDateTime.parse(tempPriceDueTime, DATE_TIME_FORMATTER);
            return dueTime.isBefore(currentTime);
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 利率获取
     *
     * @param apr
     * @param tmpApr
     * @return
     */
    private static String compareAndReturnSmaller(String apr, String tmpApr) {
        // 使用StringUtils.isEmpty()方法检查这两个字符串是否都不为空
        if (!StringUtils.isEmpty(apr) && !StringUtils.isEmpty(tmpApr)) {
            double num1 = Double.parseDouble(apr);
            double num2 = Double.parseDouble(tmpApr);

            // 返回较小的一个字符串
            return num1 < num2 ? apr : tmpApr;
        }

        // 返回不为空的字符串
        return StringUtils.isEmpty(apr) ? tmpApr : apr;
    }

    private void getCouponInfo(CouponNotifyParam param, CouponNotifyEntity entity) {
        // 查询用户优惠券列表
        DxmAllCouponListParam dxmAllCouponListParam = new DxmAllCouponListParam();
        dxmAllCouponListParam.setOpenId(param.getOpenId());
        dxmAllCouponListParam.setUserId(param.getUserId());
        try {
            DxmAllCouponListDto dxmAllCouponListDto = dxmApi.allCouponList(dxmAllCouponListParam);
            if (dxmAllCouponListDto != null && !CollectionUtils.isEmpty(dxmAllCouponListDto.getCouponList())) {
                for (DxmCouponDetail dxmCouponDetail : dxmAllCouponListDto.getCouponList()) {
                    if (StringUtils.isNotEmpty(dxmCouponDetail.getCouponId()) && dxmCouponDetail.getCouponId().equals(param.getCouponId())) {
                        LogUtil.runInfoLog("此优惠券匹配成功");
                        entity.setStartTime(dxmCouponDetail.getCouponStartTime());
                        entity.setEndTime(dxmCouponDetail.getCouponEndTime());
                        entity.setCouponType(dxmCouponDetail.getCouponType());
                        entity.setCouponName(((Map<String, String>) dxmCouponDetail.getFactors().get("101")).get("couponName"));
                        break;
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.runInfoLog("度小满回调调用优惠券列表接口失败，失败原因是：{}", e.getMessage());
        }

    }
}

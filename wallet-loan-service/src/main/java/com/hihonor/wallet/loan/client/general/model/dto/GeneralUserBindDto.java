/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general.model.dto;

import lombok.Data;

/**
 * 度小满用户准入
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
public class GeneralUserBindDto {
    /**
     * 是否准入
     */
    private Integer access;

    /**
     * 准入失败原因码
     */
    private String failCode;

    /**
     * 准入失败原因，成功无需返回
     */
    private String failReason;

    /**
     * 准入失败原因，成功无需返回,需要入库统计字段
     */
    private String refuseMsgData;

    /**
     * 准入成功返回，开放平台用户ID
     */
    private String openId;

    /**
     * 用户类型，特定渠道返回
     * 1：老用户，2-新客
     */
    private String identity;
}

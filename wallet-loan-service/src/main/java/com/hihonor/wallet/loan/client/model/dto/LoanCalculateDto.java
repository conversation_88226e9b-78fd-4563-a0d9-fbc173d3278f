package com.hihonor.wallet.loan.client.model.dto;

import java.util.List;

import com.hihonor.wallet.loan.model.dto.ContractInfoDto;
import com.hihonor.wallet.loan.model.dto.DiscountAmountInfo;
import com.hihonor.wallet.loan.model.dto.RepayPlanTermDto;

import lombok.Data;

/**
 * 功能描述
 *
 * @since 2024-03-09
 */
@Data
public class LoanCalculateDto {
    /**
     * 应还金额
     */
    private Long shouldRepayAmount;

    /**
     * 应还本金总额
     */
    private Long shouldRepayPrinAmount;

    /**
     * 应还利息总额
     */
    private Long shouldRepayInterAmount;

    /**
     * 应还服务费总额
     */
    private Long shouldRepayFeeAmount;

    /**
     * 应还其他费用总额
     */
    private Long shouldRepayMgmAmount;

    /**
     * 首次还款日
     */
    private String firstRepayDate;

    /**
     * 最后还款日
     */
    private String lastRepayDate;

    /**
     * 优惠券减免金额
     */
    private Long reductionAmount;

    /**
     * 出资方
     */
    private String stakeholders;

    /**
     * irr值
     */
    private Float irr;

    /**
     * 综合年利率，
     * 示例12.95【即12.95%】
     */
    private String annualRate;

    /**
     * 优惠前原价年利率，示例12.95【即12.95%】
     */
    private String originalRate;

    /**
     * 还款计划
     */
    private List<RepayPlanTermDto> repayPlanTerms;

    /**
     * 协议列表
     */
    private List<ContractInfoDto> contract;

    // 按期还优惠，单位：分
    private Long scheduleDiscount;

    // 限时降价优惠，单位：分
    private Long tempDiscount;

    // 总优惠(优惠劵减免金额+按期还优惠+限时降价优惠)，单位：分
    private Long totalDiscount;
}

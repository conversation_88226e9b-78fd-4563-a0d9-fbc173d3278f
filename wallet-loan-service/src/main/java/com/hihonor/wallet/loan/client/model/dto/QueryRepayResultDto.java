/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.model.dto;

import java.util.List;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
public class QueryRepayResultDto {
    /**
     * REPAYING：还款中
     * SUCCESS：还款成功
     * FAIL：还款失败
     */
    private Integer repayStatus;

    /**
     * 还款金额
     */
    private Long repayAmount;

    /**
     * 优惠券减免金额，单位：分
     */
    private Long couponAmount;

    /**
     * 成功：success
     * 还款失败：返回失败原因
     */
    private String repayResult;

    /**
     * 还款结果明细
     */
    private List<RepayResultList> repayResultList;

    /**
     * 短信验证码
     * 验证码时间5分钟之内有效
     */
    private String smsCode;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 外部平台交易流水号
     */
    private String transNo;

    /**
     * 三方订单号
     */
    private String repayOrderId;

    /**
     * 总还款金额
     */
    private Long totalAmount;

    /**
     * 还款类型
     * PRE：提前还款
     * DO：正常还款
     */
    private String repayType;

    /**
     * 绑卡Id 否，在DXM绑卡渠道必须
     */
    private String bankCardId;

    /**
     * 还款方式
     * 1: 自动代扣
     * 2: 线下对公还款
     * 3: CP侧主动还款
     * 4：荣耀侧主动还款
     */
    private Integer repayOriginType;
}

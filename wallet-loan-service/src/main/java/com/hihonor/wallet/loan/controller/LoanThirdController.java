package com.hihonor.wallet.loan.controller;

import com.alibaba.nacos.common.utils.StringUtils;
import com.hihonor.wallet.common.config.nacos.MarketingConfig;
import com.hihonor.wallet.common.constant.HeaderConstant;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.loan.client.general.model.dto.GeneralBaseDto;
import com.hihonor.wallet.loan.model.dto.LoanSupplierInfo;
import com.hihonor.wallet.loan.model.dto.TaskStatusDto;
import com.hihonor.wallet.loan.model.dto.TeddyBaseDto;
import com.hihonor.wallet.loan.model.dto.UserStatusResponse;
import com.hihonor.wallet.loan.model.param.LoanAdsMonitorParam;
import com.hihonor.wallet.loan.model.param.QueryCpListParam;
import com.hihonor.wallet.loan.model.param.QueryTaskStatusParam;
import com.hihonor.wallet.loan.model.param.QueryUserStatusParam;
import com.hihonor.wallet.loan.model.param.SmsSignatureParam;
import com.hihonor.wallet.loan.service.LoanThirdService;
import com.hihonor.wallet.loan.service.UserService;

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;

/**
 * 第三方接口
 *
 * <AUTHOR>
 * @since 2024/7/2 10:51
 */
@RestController
@RequestMapping("/third")
public class LoanThirdController {

    @Autowired
    private LoanThirdService loanThirdService;

    @Autowired
    private UserService userService;

    @Autowired
    private MarketingConfig marketingConfig;

    /**
     * 营销平台查询用户借贷任务状态
     *
     * @param param 营销平台查询用户借贷任务状态入参
     * @return 借贷任务状态结果
     */
    @PostMapping("/marketing/queryTaskStatus")
    public ResponseResult<TaskStatusDto> queryTaskStatus(@RequestBody @Validated QueryTaskStatusParam param) {
        //验签
        this.signVerify(param);
        TaskStatusDto dto = loanThirdService.queryTaskStatus(param);
        return ResponseResult.success(dto);
    }

    /**
     * 获取cp名称序列
     *
     * @param param 获取cp名称序列
     * @return list
     */
    @PostMapping("/marketing/queryCpList")
    public ResponseResult<List<LoanSupplierInfo>> queryCpList(@RequestBody QueryCpListParam param) {
        //验签
        this.signVerify(param);
        List<LoanSupplierInfo> loanSupplierInfos = loanThirdService.getSupplierList();
        return ResponseResult.success(loanSupplierInfos);
    }

    /**
     * 营销平台查询用户状态
     *
     * @param param 营销平台查询用户借贷任务状态入参
     * @return 借贷任务状态结果
     */
    @PostMapping("/marketing/queryUserStatus")
    public ResponseResult<UserStatusResponse> queryUserStatus(@RequestBody @Validated QueryUserStatusParam param) {
        //验签
        this.signVerify(param);
        UserStatusResponse response = loanThirdService.queryUserStatus(param);
        return ResponseResult.success(response);
    }


    /**
     * 商推监测接口
     *
     * @param param param
     * @return
     */
    @GetMapping("/ads/monitorNotify")
    public ResponseResult<?> adsMonitorNotify(@Validated LoanAdsMonitorParam param) {
        userService.adsMonitorNotify(param);
        return ResponseResult.success();
    }

    /**
     * 三方借贷公司通知接口
     *
     * @param param param
     * @return GeneralBaseDto
     */
    @PostMapping("/notify")
    public GeneralBaseDto notify(@RequestBody String param) {
        GeneralBaseDto dto = loanThirdService.thirdNotify(param);
        return dto;
    }

    @PostMapping("/teddy/pushSmsSignature")
    public ResponseResult<TeddyBaseDto> pushSmsSignature(@RequestBody @Validated SmsSignatureParam param) {
        TeddyBaseDto dto = loanThirdService.pushSmsSignature(param);
        return ResponseResult.success();
    }


    /**
     * 校验签名
     *
     * @param param 入参
     */
    private void signVerify(Object param) {
        // 获得请求头签名参数
        HttpServletRequest request = RequestUtils.getCurrentRequest();
        String sign = null;
        if (Objects.nonNull(request)) {
            sign = request.getHeader(HeaderConstant.SIGN);
        }

        if (StringUtils.isBlank(sign)) {
            LogUtil.runInfoLog("signVerifyForQueryTaskStatus.error，missing sign");
            throw new BusinessException(WalletResultCode.SIGN_INVALID);
        }

        StringBuilder signBuilder = new StringBuilder();
        Map<String, Object> argsMap = JsonUtils.objectToMap(param);
        TreeMap<String, Object> sortedMap = new TreeMap<>(argsMap);
        Set<String> keySet = sortedMap.keySet();
        for (String key : keySet) {
            Object val = argsMap.get(key);
            if (val == null) {
                continue;
            }
            signBuilder.append("&").append(key).append("=").append(val);
        }

        String signStr = signBuilder.toString();
        if (signStr.startsWith("&")) {
            signStr = signStr.substring(1);
        }

        // 兼容就接口，在sign不为空时校验接口数据签名
        String signKey = marketingConfig.getPublicKey();
        String signHmacSha256 = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, signKey).hmacHex(signStr);
        if (!sign.equals(signHmacSha256)) {
            LogUtil.runSensitiveInfoLog("signVerifyForQueryTaskStatus.signParam:{}", signStr);
            //签名校验不通过，直接返回，不做业务处理
            throw new BusinessException(WalletResultCode.SIGN_INVALID);
        }
    }
}

/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.dxm.constant;

import java.util.*;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public class DxmConstant {

    //借款状态
    public static final Map<Integer, String> LOAN_STATUS = new HashMap<Integer, String>(5) {{
        put(1, "APPLYING");
        put(2, "PAYING");
        put(3, "OVERDUE");
        put(4, "PAYOFF");
        put(5, "FAIL");
    }};

    //借款状态
    public static final Map<String, Integer> LOAN_STATUS_DXM = new HashMap<String, Integer>(5) {{
        put("APPLYING", 1);
        put("PAYING", 2);
        put("OVERDUE", 3);
        put("PAYOFF", 4);
        put("FAIL", 5);
    }};

    //是否可还款状态dxm
    public static final Map<String, Integer> WHETHER_TO_REPAY_STATUS_DXM = new HashMap<String, Integer>(2) {{
        put("REPAY", 2);
        put("BAN_REPAY", 7);
    }};

    //还款状态dxm
    public static final Map<String, Integer> REPAY_STATUS_DXM = new HashMap<String, Integer>(2) {{
        put("REPAYING", 1);
        put("SUCCESS", 2);
        put("FAIL", 4);
    }};


    //获取协议阶段
    public static final Map<Integer, String> CONTRACT_STATUS = new HashMap<Integer, String>(4) {{
        put(1, "CREDIT");
        put(2, "BIND");
        put(3, "LOAN");
        put(4, "REDISTRIBUTE");
    }};

    //还款方式
    public static final Map<Integer, String> REPAY_METHOD = new HashMap<Integer, String>(4) {{
        put(1, "FIXED_INSTALLMENT");
        put(2, "FIXED_PRINCIPAL");
        put(3, "FIXED_XXHB_TYPE_NEW");
        put(4, "FIXED_INTEREST_ON_SCHEDULE");
    }};

    //还款方式dxm
    public static final Map<String, Integer> REPAY_METHOD_DXM = new HashMap<String, Integer>(4) {{
        put("FIXED_INSTALLMENT", 1);
        put("FIXED_PRINCIPAL", 2);
        put("FIXED_XXHB_TYPE_NEW", 3);
        put("FIXED_INTEREST_ON_SCHEDULE", 4);
    }};

    //当期状态
    public static final Map<String, Integer> TERM_STATUS_DXM = new HashMap<String, Integer>(5) {{
        put("PAYOFF", 1);
        put("PAYING", 2);
        put("PART_PAID", 3);
        put("OVERDUE", 4);
        put("GRACE", 5);
    }};

    public static final Map<String, String> LOAN_USE_DXM = new HashMap<String, String>(6) {{
        put("RCXF", "个人日常消费");
        put("ZX", "房屋装修");
        put("LY", "旅游出行");
        put("JX", "在职深造");
        put("JKYL", "健康医疗");
        put("Others", "其他消费");
    }};

    public static final Map<String, String> UNUSABLE_REASON = new HashMap<String, String>(36) {{
        put("1000", "不符合使用规则");
        put("1001", "不符合分期数");
        put("1002", "不符合借款金额");
        put("1003", "新客专享");
        put("1004", "不符合借款后N天可用");
        put("1005", "必须借款时绑定");
        put("1006", "必须还款结清时可用");
        put("1007", "不符合临时价格限制");
        put("2001", "优惠金额为0");
        put("2002", "该费用类型只能用一张券");
        put("2003", "借据逾期不可用券");
        put("2004", "当前渠道不可用此券");
        put("2007", "仅限锁期还款方式可用");
        put("2008", "必须按期还款可用");
        put("2009", "未到优惠指定还款期数");
        put("2010", "优惠券已使用");
        put("2012", "必须结清当期使用");
        put("2013", "逾期分期不可用券");
        put("2014", "当前出资机构暂不可用");
        put("2015", "结清逾期分期可用");
        put("2016", "催收减免不可用券");
        put("2017", "已过期");
        put("2018", "借据已绑定非逾期券，不可新增非逾期券");
        put("2019", "不支持转红包机构使用");
        put("2020", "累计优惠金额已达优惠上限");
        put("2021", "仅贴息借据可用");
        put("2022", "当前客户端不可用");
        put("2023", "法诉借据不可用券");
        put("2024", "随借随还借据不可用");
        put("2025", "按期还款借据不可用");
        put("2026", "按期还款借据不可用");
        put("2027", "领取后方可使用");
        put("2028", "按期还款借据不可用");
        put("2029", "不符合借款金额");
        put("2030", "不符合分期数");
        put("2031", "不符合产品线限制");
    }};

    public static final String UNUSABLE_REASON_GUARANTEE = "优惠券不可用";

    public static final String OverDue = "overDue";

    public static final String FixedPeriod = "fixedPeriod";

    public static final String BorrowAndRepay = "borrowAndRepay";

    public static final String USER_BIND_URI = "query.user.bind";

    public static final String BIND_BANKCARD_LIST_URI = "bind.bankcard.list";

    public static final String SUPPORT_BANKCARD_LIST_URI = "support.bankcard.list";

    public static final String BIND_BANKCARD_URI = "bind.bankcard";

    public static final String BIND_SMS_VERIFY_URI = "bind.sms.verify";

    public static final String LIMIT_APPLY_URI = "limit.apply";

    public static final String CREDIT_APPLY_RESULT_URI = "credit.apply.result";

    public static final String USER_CREDIT_INFO_URI = "user.credit.info";

    public static final String LOAN_TRIAL_URI = "loan.trial";

    public static final String LOAN_VERIFY_LIST_URI = "loan.verify.list";

    public static final String LOAN_APPLY_URI = "loan.apply";

    public static final String LOAN_RESULT_URI = "loan.result";

    public static final String LOAN_RECORD_URI = "loan.record";

    public static final String LOAN_RECORD_DETAIL_URI = "loan.record.detail";

    public static final String REPAY_PLAN_URI = "repay.plan";

    public static final String REPAY_CALCULATE_URI = "repay.calculate";

    public static final String REPAY_DO_URI = "repay.do";

    public static final String REPAY_RESULT_URI = "repay.result";

    public static final String AGREEMENT_QUERY_URI = "agreement.query";

    public static final String CONTRACT_DETAIL_QUERY_URI = "contract.detail.query";

    public static final String ALL_COUPON_LIST_URI = "all.coupon.list";

    public static final String LOAN_COUPON_URI = "loan.coupon";

    public static final String QUERY_ADJUST_INFO_URI = "query.adjust.info";

    public static final String SETTLEMENT_QUERY_URI = "settlement.query";

    public static final String SETTLEMENT_SEND_URI = "settlement.send";

    public static final String REPAY_RECORD_URI = "repay.record";

    public static final String RESIGN_CHECK_URI = "resign.check";

    public static final String SEND_SMS_CODE_URI = "send.sms.code";

    public static final String VERIFY_SMS_CODE_URI = "verify.sms.code";

    public static final String ID_CARD_CHECK = "idcard.check";

    public static final String USER_ACCOUNT_CANCEL = "user.cancel.account";

    public static final String USER_CANCEL_CHECK = "user.cancel.check";

    public static final String ADD_CREDIT_URL_QUERY = "addcredit.url.query";

    public static final String CREATE_ACTIVITY_URL = "create.activity.url";

    public static final String CHANGE_PHONE_URL = "change.phone";

    /**
     * 确定yes
     */
    public static final String YES = "Y";

    /**
     * 否定no
     */
    public static final String NO = "N";

    /**
     * 还款类型
     */
    public class RepayType {
        /**
         * 提前还款
         */
        public static final String EARLY_REPAY = "PRE";

        /**
         * 正常还款
         */
        public static final String NORMAL_REPAY = "DO";
    }

    /**
     * 授信状态
     */
    public static class CreditApplyResult {
        /**
         * 通过
         */
        public static final String PASS = "PASS";
        /**
         * 拒绝
         */
        public static final String REFUSE = "REFUSE";

        /**
         * 无效订单
         */
        public static final String NOORDER = "NOORDER";

        /**
         * 审核中
         */
        public static final String AUDITING = "AUDITING";

        /**
         * 等待申请结果
         */
        public static final String WAIT = "WAITING";
    }

    /**
     * 新老客标识
     */
    public class Identify {
        /**
         * 老客
         */
        public static final String OLD = "OLD";

        /**
         * 新客
         */
        public static final String NEW = "NEW";
    }

    /**
     * 还款状态
     */
    public class RepayStatus {
        /**
         * 还款中
         */
        public static final String REPAYING = "REPAYING";

        /**
         * 还款失败
         */
        public static final String FAIL = "FAIL";

        /**
         * 还款成功
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 部分借据还款成功
         */
        public static final String PART = "PART";
    }


    /**
     * 额度状态
     */
    public class LimitStatus {
        /**
         * 没有额度
         */
        public static final String EXPIRED = "EXPIRED";

        /**
         * 正常
         */
        public static final String ACTIVE = "ACTIVE";

        /**
         * 审核
         */
        public static final String AUDITING = "AUDITING";

        /**
         * 拒绝
         */
        public static final String REFUSE = "REFUSE";

        /**
         * 再分发升级
         */
        public static final String UPGRADING = "UPGRADING";
    }

    /**
     * 额度使用状态
     */
    public class LimitUseErrStatus {
        /**
         * 存在放款中借款单
         */
        public static final String LENDING_ERR = "LENDING_ERR";

        /**
         * 存在逾期订单不可借款
         */
        public static final String OVERDUE_ERR = "OVERDUE_ERR";

        /**
         * 其他原因
         */
        public static final String OTHER_ERR = "OTHER_ERR";

        /**
         * 暂时无法支用
         */
        public static final String UNAVAILABLE_ERR = "UNAVAILABLE_ERR";

        /**
         * 存在补录中的订单
         */
        public static final String VERIFY_ERR = "VERIFY_ERR";
    }

    public class LoanStatus {
        /**
         * 申请中
         */
        public static final String APPLYING = "APPLYING";

        /**
         * 成功
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 失败（取消、撤销状态）
         */
        public static final String FAIL = "FAIL";

        /**
         * 拒绝
         */
        public static final String REFUSE = "REFUSE";

        /**
         * 待放款
         */
        public static final String LENDING = "LENDING";

        /**
         * 取消订单
         */
        public static final String CANCEL = "CANCEL";
    }

    public class LoanVerifyStatus {
        /**
         * 不允许借款
         */
        public static final String REFUSE = "REFUSE";

        /**
         * 鉴权后可借款
         */
        public static final String VERIFY = "VERIFY";

        /**
         * 等待流程返回,此状态需要继续轮询
         */
        public static final String WAIT = "WAIT";
    }

    public class LoanTermStatus {
        /**
         * 已结清
         */
        public static final String PAYOFF = "PAYOFF";

        /**
         * 待还款
         */
        public static final String PAYING = "PAYING";

        /**
         * 已逾期
         */
        public static final String OVERDUE = "OVERDUE";

        /**
         * 部分已还
         */
        public static final String PART_PAID = "PART_PAID";

        /**
         * 宽限期
         */
        public static final String GRACE = "GRACE";
    }

    /**
     * 优惠券使用状态
     */
    public class CouponStatus {
        /**
         * 优惠券使用成功
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 优惠券使用失败
         */
        public static final String FAIl = "FAIL";
    }

    /**
     * 还款方式
     */
    public class RepayMethod {
        /**
         * 等额本息
         */
        public static final String FIXED_INSTALLMENT = "FIXED_INSTALLMENT";

        /**
         * 等额本金
         */
        public static final String FIXED_PRINCIPAL = "FIXED_PRINCIPAL";

        /**
         * 锁期
         */
        public static final String FIXED_INTEREST_ON_SCHEDULE = "FIXED_INTEREST_ON_SCHEDULE";


        /**
         * 先息后本
         */
        public static final String FIXED_XXHB_TYPE_NEW = "FIXED_XXHB_TYPE_NEW";
    }

    /**
     * 额度类型
     */
    public class LimitType {
        /**
         * 循环额度
         */
        public static final String CIRCLE = "CIRCLE";
    }

    /**
     * 回调data域
     */
    public class CallBackDataCode {
        /**
         * 成功
         */
        public static final String SUCCESS = "Y";

        /**
         * 失败
         */
        public static final String FAIL = "N";
    }

    public class DxmErrorCode {
        /**
         * 本期借据已结清，请勿重复还款
         */
        public static final String LOAN_SETTLED = "";

        /**
         * 重复还款
         */
        public static final String DUPLICATE_PAYMENT = "";
        /**
         * 新锁期借据不可部分还当期
         */
        public static final String LOCK_LOAN_NO_PARTIAL_REPAYMENT_FOR_NOW = "445050";

        /**
         * 新锁期借据不可部分还未来期
         */
        public static final String LOCK_LOAN_NO_PARTIAL_REPAYMENT_FOR_FEATURE = "445051";

        /**
         * 未查询到相关交易
         */
        public static final String NO_TRANSACTION_FOUND = "106003";

        /**
         * 优惠金额与实际不符
         */
        public static final String DISCOUNT_MISMATCH = "2003";

        /**
         * 还款金额过大（dxm返回444023）
         */
        public static final String HIGH_REPAYMENT_SUM = "444023";

        /**
         * 存在未结束的还款订单（dxm返回445014）
         */
        public static final String UNSETTLED_REPAYMENT = "445014";

        /**
         * 新锁期借据部分，已被拦截
         */
        public static final String PARTIAL_INTERCEPT = "520181";

        /**
         * 内部服务异常
         */
        public static final String SYSTEM_ERROR = "401003";

        /**
         * 参数错误
         */
        public static final String PARAMETER_ERROR = "101001";

        /**
         * 借款金额验证未通过，请稍后再试（dxm返回106001）
         */
        public static final String LOAN_TRAIL_ERROR = "106001";

        /**
         * 在度小满存在放款中订单（dxm返回411082）
         */
        public static final String EXIST_LOAN_IN_PROGRESS = "411082";

        /**
         * 发起订单号重复（dxm返回102041）
         */
        public static final String DUPLICATE_ORDER_NUMBER = "102041";

        /**
         * 四项信息加解密失败（手机号、姓名、身份证、银行卡）（dxm返回600000004）
         */
        public static final String ENCRYPT_DECRYPT_FAIL = "600000004";

        /**
         * 用户流程状态异常（dxm返回102047）
         */
        public static final String USER_PROCESS_STATUS_ABNORMAL = "102047";

        /**
         * 订单已失效，请重新发起一笔新的订单（dxm返回102065）
         */
        public static final String ORDER_INVALID = "102065";

        /**
         * 借据已结清，请勿重复还款
         */
        public static final String LOAN_SETTLED_PLEASE_NOT_REPAY = "106015";

        /**
         * 当前借据处于禁还期
         */
        public static final String THE_LOAN_IS_BAN = "106017";

        /**
         * 验证码频繁发送
         */
        public static final String SMS_SEND_FREQUETN = "40003";

        /**
         * 用户已注销
         */
        public static final String USER_CANCEL_ACCOUNT = "3008";
    }

    public class LoanRecordStatus {
        /**
         * 申请中
         */
        public static final int RECORD_APPLYING = 1;

    }

    public class repaymentType{
        /**
         * 提前还款
         */
        public static final int prePayment = 1;

        /**
         * 全部结清
         */
        public static final int fullSettlement = 2;

        /**
         * 还本期
         */
        public static final int payBackPeriod = 3;

        /**
         * 还部分逾期
         */
        public static final int PartialOverdue = 4;

        /**
         * 还部分逾期
         */
        public static final int overdue = 5;
    }


    public static final Map<String, List<Integer>> supportRepayTypeMap = new HashMap<String,  List<Integer>>(3) {{
        put(BorrowAndRepay, new ArrayList<>(Arrays.asList(repaymentType.prePayment, repaymentType.payBackPeriod)));
        put(FixedPeriod, new ArrayList<>(Arrays.asList(repaymentType.fullSettlement, repaymentType.payBackPeriod)));
        put(OverDue , new ArrayList<>(Arrays.asList(repaymentType.PartialOverdue, repaymentType.overdue)));
    }};

    public static class DXMChangeType {
        public final static Map<String, Integer> DXMLimitChangeType = new HashMap<String, Integer>() {{
            //提额
            put("1001", 1);
            //降低
            put("1002", 2);
        }};
        public final static Map<String, Integer> DXMRateChangeType = new HashMap<String, Integer>() {{
            //提高
            put("2001", 1);
            //调低
            put("2002", 2);
        }};
    }
}

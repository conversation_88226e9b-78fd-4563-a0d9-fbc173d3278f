package com.hihonor.wallet.loan.controller;

import java.util.List;

import com.hihonor.wallet.loan.model.param.BankCardQueryParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.loan.model.dto.BankCardBindDto;
import com.hihonor.wallet.loan.model.dto.BankCardDto;
import com.hihonor.wallet.loan.model.dto.BankDto;
import com.hihonor.wallet.loan.model.dto.BindSmsVerifyResDto;
import com.hihonor.wallet.loan.model.param.BankCardBindParam;
import com.hihonor.wallet.loan.model.param.BankCardBindVerifyParam;
import com.hihonor.wallet.loan.service.BankCardService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Api(value = "bankCard.BankCardController", tags = {"银行卡"})
@RestController("bankCardController")
@RequestMapping("/api/bankcard")
public class BankCardController {

    @Autowired
    private BankCardService bankCardService;

    /**
     * 查询用户已绑定的银行卡列表
     */
    @ApiOperation(value = "查询用户已绑定的银行卡列表")
    @PostMapping("/list")
    public ResponseResult<List<BankCardDto>> bankCardList(@RequestBody @Validated BankCardQueryParam param) {
        List<BankCardDto> bankCardDtos = bankCardService.bankCardList(param);
        return ResponseResult.success(bankCardDtos);
    }

    /**
     * 查询支持的银行列表
     */
    @ApiOperation(value = "查询支持的银行列表")
    @PostMapping("/supportList")
    public ResponseResult<List<BankDto>> supportList(@RequestBody @Validated BankCardQueryParam param) {
        List<BankDto> bankDtos = bankCardService.supportList(param);
        return ResponseResult.success(bankDtos);
    }

    /**
     * 银行卡绑定
     */
    @ApiOperation(value = "银行卡绑定")
    @PostMapping("/bind")
    public ResponseResult<BankCardBindDto> bankCardBind(@RequestBody @Validated BankCardBindParam param) {
        BankCardBindDto bankCardBindDto = bankCardService.bankCardBind(param);
        return ResponseResult.success(bankCardBindDto);
    }


    /**
     * 银行卡绑定短信验证
     */
    @ApiOperation(value = "银行卡绑定短信验证")
    @PostMapping("/bindSmsVerify")
    public ResponseResult<BindSmsVerifyResDto> bindSmsVerify(@RequestBody @Validated BankCardBindVerifyParam param) {
        BindSmsVerifyResDto bindSmsVerifyResDto = bankCardService.bindSmsVerify(param);
        return ResponseResult.success(bindSmsVerifyResDto);
    }

}

/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.model.dto;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
public class RepayItems {
    /**
     * 期次
     */
    private int termNo;

    /**
     * 本期应还总额,单位：分
     */
    private Long termAmount;

    /**
     * 本期应还本金，单位：分
     */
    private Long termPrincipal;

    /**
     * 本期应还利息，单位：分
     */
    private Long termInterest;

    /**
     * 本期还款本金罚息，单位：分
     */
    private Long termPrinPenalty;

    /**
     * 本期还款利息罚息，单位：分
     */
    private Long termInterPenalty;

    /**
     * 本期应还服务费，单位：分
     */
    private Long termFee;

    /**
     * 本期优惠金额，单位：分
     */
    private Long termDiscount;

    /**
     * 本期还款违约金，单位：分
     */
    private Long termViolateFee;

    /**
     * 本期应还逾期费
     */
    private Long termOverdue;

    /**
     * 本期还款服务费
     */
    private Long termServiceFee;
}

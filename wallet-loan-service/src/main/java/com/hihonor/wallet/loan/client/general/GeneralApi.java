/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general;

import com.hihonor.wallet.loan.client.config.LoanClientConfigParam;
import com.hihonor.wallet.loan.client.general.model.dto.*;
import com.hihonor.wallet.loan.client.general.model.param.*;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface GeneralApi {
    /**
     * 度小满 用户准入
     *
     * @param param param
     * @return
     */
    GeneralUserAccessDto userAccess(GeneralUserAccessParam param);


    /**
     * 账号绑定申请
     *
     * @param param param
     * @return
     */
    GeneralUserBindDto userBind(GeneralUserBindParam param);

    /**
     * 用户绑卡列表
     *
     * @param param param
     * @return DxmBindBankCardListDto
     */
    GeneralBindBankCardListDto bindBankCardList(GeneralParam param);

    /**
     * 授信申请
     *
     * @param param param
     * @return DxmLimitApplyDto
     */
    GeneralCreditApplyDto limitApply(GeneralCreditApplyParam param);

    /**
     * 授信申请结果查询
     *
     * @param param param
     * @return DxmCreditApplyResultDto
     */
    GeneralCreditApplyResultDto creditApplyResult(GeneralCreditApplyResultParam param);

    /**
     * 支持的银行列表查询
     *
     * @param param param
     * @return DxmBindBankCardListDto
     */
    GeneralSupportBankCardListDto supportBankCardList(GeneralParam param);

    /**
     * 银行卡绑定
     *
     * @param param param
     * @return DxmBindBankCardDto
     */
    GeneralBindBankCardDto bindBankCard(GeneralBindBankCardParam param);

    /**
     * 额度查询
     *
     * @param param param
     * @return DxmUserCreditInfoDto
     */
    GeneralUserCreditInfoDto userCreditInfo(GeneralParam param);

    /**
     * 借款试算
     *
     * @param param param
     * @return DxmLoanTrialDto
     */
    GeneralLoanTrialDto loanTrial(GeneralLoanTrialParam param);

    /**
     * 交易鉴权
     *
     * @param param param
     * @return DxmLoanVerifyListDto
     */
    GeneralLoanVerifyListDto loanVerifyList(GeneralLoanVerifyListParam param);

    /**
     * 借款申请（交易验证信息补充）
     *
     * @param param param
     * @return DxmLoanApplyDto
     */
    GeneralLoanApplyDto loanApply(GeneralLoanApplyParam param);

    /**
     * 借款结果查询
     *
     * @param param param
     * @return DxmLoanResultDto
     */
    GeneralLoanResultDto loanResult(GeneralLoanResultParam param);

    /**
     * 借款列表记录查询
     *
     * @param param param
     * @return DxmLoanRecordDto
     */
    GeneralLoanRecordListDto loanRecord(GeneralLoanRecordParam param);

    /**
     * 借款记录详情查询
     *
     * @param param param
     * @return DxmLoanRecordDetailDto
     */
    GeneralLoanRecordDetailDto loanRecordDetail(GeneralLoanRecordDetailParam param);

    /**
     * 关闭借款订单
     */
    GeneralCloseLoanOrderDto closeLoanOrder(GeneralCloseLoanOrderParam param);

    /**
     * 查询待还款计划
     *
     * @param param param
     * @return DxmRepayPlanDto
     */
    GeneralRepayPlanDto repayPlan(GeneralRepayPlanParam param);

    /**
     * 还款试算
     *
     * @param param param
     * @return DxmRepayCalculateDto
     */
    GeneralRepayCalculateDto repayCalculate(GeneralRepayCalculateParam param);

    /**
     * 发起主动还款
     *
     * @param param param
     */
    GeneralRepayDoDto repayDo(GeneralRepayDoParam param);

    /**
     * 还款结果查询
     *
     * @param param param
     * @return DxmRepayResultDto
     */
    GeneralRepayResultDto repayResult(GeneralRepayResultParam param);

    /**
     * 获取协议列表
     *
     * @param param param
     * @return DxmAgreementQueryDto
     */
    GeneralAgreementQueryDto agreementQuery(GeneralAgreementQueryParam param);

    /**
     * 已签署协议详情
     *
     * @param param param
     * @return DxmContractDetailQueryDto
     */
    GeneralContractDetailQueryDto contractDetailQuery(GeneralContractDetailQueryParam param);

    /**
     * 可用优惠券(包括借款和还款)
     *
     * @param param param
     * @return DxmAllCouponListDto
     */
    GeneralAllCouponListDto allCouponList(GeneralAllCouponListParam param);

    /**
     * 借款可用优惠券2
     *
     * @param param param
     * @return DxmLoanCouponDto
     */
    GeneralLoanCouponDto loanCoupon(GeneralLoanCouponParam param);

    /**
     * 实时额度调整查询接口
     *
     * @param param param
     * @return DxmQueryAdjustInfoDto
     */
    GeneralQueryAdjustInfoDto queryAdjustInfo(GeneralQueryAdjustInfoParam param);

    /**
     * 查询是否可以开具结清证明
     *
     * @param param param
     * @return DxmSettlementQueryDto
     */
    GeneralSettlementQueryDto settlementQuery(GeneralSettlementQueryParam param);

    /**
     * 发送结清证明到邮箱
     *
     * @param param param
     * @return DxmSettlementSendDto
     */
    GeneralSettlementSendDto settlementSend(GeneralSettlementSendParam param);

    /**
     * 查询还款记录
     *
     * @param param param
     * @return DxmRepayRecordDto
     */
    GeneralRepayRecordDto repayRecord(GeneralRepayRecordParam param);

    /**
     * 支付前查询接口
     *
     * @param param param
     * @return DxmResignCheckDto
     */
    GeneralResignCheckDto resignCheck(GeneralResignCheckParam param);

    /**
     * 加密
     *
     * @param dxmBaseParam          dxmBaseParam
     * @param loanClientConfigParam loanClientConfigParam
     * @return String
     */
    String handelGeneralmData(GeneralBaseParam dxmBaseParam, LoanClientConfigParam loanClientConfigParam);

    /**
     * 获取 解密后的数据
     *
     * @param response              response
     * @param loanClientConfigParam loanClientConfigParam
     * @param portName              portName
     * @return DxmBaseDto
     */
    GeneralBaseDto getEncryptData(String response, LoanClientConfigParam loanClientConfigParam, String portName);

    /**
     * 发送短信
     *
     * @param param param
     * @return DxmSendSmsCodeDto
     */
    GeneralSendSmsCodeDto sendSmsCode(GeneralSendSmsCodeParam param);

    /**
     * 验证验证码
     *
     * @param param param
     * @return DxmVerifySmsCodeDto
     */
    GeneralVerifySmsCodeDto verifySmsCode(GeneralVerifySmsCodeParam param);

    /**
     * 银行卡绑卡短信验证
     *
     * @param param param
     * @return DxmBindSmsVerifyDto
     */
    GeneralBindSmsVerifyDto bindSmsVerify(GeneralBindSmsVerifyParam param);

    /**
     * 回调用的 解密方法
     *
     * @param req request
     * @return DxmBaseParam
     */
    GeneralBaseParam getEncryptDataForNotify(String req);

    /**
     * 身份证识别
     *
     * @param param param
     * @return DxmIdCardCheckDto
     */
    GeneralIdCardCheckDto checkIdCard(GeneralIdCardCheckParam param);

    /**
     * 用户注销检查
     *
     * @param param
     * @return
     */
    GeneralUserCancelAccountDto userCancelAccount(GeneralUserCancelAccountParam param);


    /**
     * 用户注销
     *
     * @param param
     * @return
     */
    GeneralUserLogoffDto userLogoff(GeneralUserLogoffParam param);

    /**
     * 获取增信H5
     *
     * @param param
     * @return
     */
    GeneralQueryAddCreditUrlDto queryAddUrl(GeneralQueryAddCreditUrlParam param);

    GeneralSignUrlDto querySignUrl(GeneralSignUrlParam param);

    /**
     * 签名排序
     *
     * @param jsonObject
     * @return
     */
    String getSign(Object jsonObject);

    /**
     * 修改手机号
     */
    GeneralModifyPhoneDto modifyPhone(GeneralModifyPhoneParam generalModifyPhoneParam);
}

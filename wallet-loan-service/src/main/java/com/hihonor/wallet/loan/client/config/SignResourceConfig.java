package com.hihonor.wallet.loan.client.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RefreshScope
@Data
@ConfigurationProperties(prefix = "withholdsign")
public class SignResourceConfig {
    private List<SignResourceConfigParam> resourceList;

    public SignResourceConfigParam getSignResourceConfig(Integer signStatus) {
        for (SignResourceConfigParam configParam : resourceList) {
            if (signStatus != null && signStatus.equals(configParam.getSignStatus())) {
                return configParam;
            }
        }
        return null;
    }
}

/*
 * Copyright (c) Honor Terminal Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.hihonor.wallet;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 小贷服务
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableFeignClients(basePackages = "com.hihonor.wallet")
@ComponentScan("com.hihonor.wallet")
@SpringBootApplication
public class WalletLoanApplication {
    public static void main(String[] args) {
        SpringApplication.run(WalletLoanApplication.class, args);
    }
}

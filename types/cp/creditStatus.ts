import { ProductInfo } from "@/app/cp/creditInfoQuery";
import { CreditApplyStatusEnum } from "@/app/cp/creditApply/type";

export interface CreditStatus {
  applyStatus: number;
  refuseCode?: string;
  refuseMsg?: string;
  refuseMsgData?: string;
  outOrderNo?: string;
  creditLimit: number;
  remainLimit: number;
  applyTime: number;
  productInfos: ProductInfo[];
}

export interface CreditActions {
  // 授信结果
  creditApplyStatus: CreditApplyStatusEnum;
  creditLimit: number;
}
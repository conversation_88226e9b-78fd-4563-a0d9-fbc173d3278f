import { ProductInfo } from "@/app/cp/creditInfoQuery";

export interface CreditStatus {
  applyStatus: number;
  refuseCode?: string;
  refuseMsg?: string;
  refuseMsgData?: string;
  outOrderNo?: string;
  creditLimit: number;
  remainLimit: number;
  applyTime: number;
  productInfos: ProductInfo[];
}

export enum CreditApplyStatusEnum {
  // 审核中
  PENDING = 1,
  // 通过
  PASSED = 2,
  // 拒绝
  REJECTED = 3,
}

export const creditApplyStatusNameMap = new Map<number, string>([
  [1, "审核中"],
  [2, "通过"],
  [3, "拒绝"],
]);

export interface CreditActions {
  // 授信结果
  creditApplyStatus: CreditApplyStatusEnum;
  creditLimit: number;
}

export interface CreditInfo {
  // 当前状态
  status: CreditStatus;

  // 状态模拟
  actions: CreditActions;
}
import { JsonValue } from "type-fest";
import { BankCardInfo } from "@/types/cp/bankCard";
import { LoanInfo } from "@/types/cp/loanInfo";
import { CreditInfo } from "@/types/cp/creditInfo";

export interface LoanUser {
  id?: number;
  userId: string;
  realName: string;
  ctfCode: string;
  mobileNo: string;
  creditInfo: CreditInfo;
  bankCardInfo: BankCardInfo;
  loanInfo: LoanInfo;
  createTime: bigint;
}

export const loanUserStatusNameMap = {
  // 已准入未授信
  0: "已准入未授信",
  1: "审核中",
  2: "授信通过",
  3: "授信拒绝",
};

export interface InterfaceLog {
  id?: number;
  userId: number;
  interfaceName: string;
  requestData: JsonValue;
  responseData: JsonValue;
  createTime: Date;
}
export interface LoanInfo {
  status: LoanInfoStatus;
  action: LoanInfoAction;
}

interface LoanInfoStatus {
  loanApplyRecords: LoanRecordDetail[];
  loanOrders: LoanOrderDetail[];
}

interface LoanInfoAction {
  loanApplyStatus: LoanApplyStatusEnum;
  verifyList: LoanVerifyItemEnum[]
}

export enum LoanApplyStatusEnum {
  /** 初始状态（已交易鉴权未提交，CP订单还在有效期） */
  INIT = 0,
  /** 审核中 */
  PENDING = 1,
  /** 成功 */
  SUCCESS = 2,
  /** 失败（申请审核失败） */
  FAILED = 3,
  /** 拒绝 */
  REJECTED = 4,
  /** 取消（放款失败） */
  CANCELED = 5,
  /** 需鉴权（用信补录，补签协议后再提交借款） */
  NEED_AUTH = 6,
  /** 已交易鉴权未提交，CP缓存订单失效 */
  CACHE_EXPIRED = 7
}

export const loanApplyStatusNameMap = new Map<number, string>([
  [0, "初始状态"],
  [1, "审核中"],
  [2, "成功"],
  [3, "失败"],
  [4, "拒绝"],
  [5, "取消"],
  [6, "需鉴权"],
  [7, "已交易鉴权未提交，CP缓存订单失效"],
]);

export enum LoanVerifyItemEnum {
  /** 活体，通过fileInfos传给渠道 */
  FACE_CHECK = 'FACE_CHECK',

  /** 身份证OCR，通过fileInfos传给渠道 */
  ID_CARD_OCR = 'ID_CARD_OCR',

  /** 短信验证，发送短信验证码，完成短信验证码验证 */
  SMS = 'SMS',

  /** 联系人，通过relationInfos传给渠道 */
  BASICINFO_FILL = 'BASICINFO_FILL',

  /** 联系人2，通过relationInfos传给渠道（联系人2） */
  BASICINFO_FILL2 = 'BASICINFO_FILL2',

  /** 居住地，通过residentialAddr传给渠道（会返回省+市+县+详细地址） */
  RESIDENTIAL_ADDR = 'RESIDENTIAL_ADDR',

  /** 家庭住址，通过familyAddr传给渠道（会返回省+市+县+详细地址），如果需要详细地址，需要同时下发RESIDENTIAL_ADDR和FAMILY_ADDR */
  FAMILY_ADDR = 'FAMILY_ADDR',

  /** 教育程度，通过education传给渠道 */
  EDUCATION = 'EDUCATION',

  /** 工作单位，通过company传给渠道 */
  COMPANY = 'COMPANY',

  /** 职业收入，通过career，income传给渠道 */
  CAREER_INCOME = 'CAREER_INCOME',

  /** 职业，通过career传给渠道 */
  CAREER = 'CAREER',

  /** 婚姻状态，通过marriage传给渠道 */
  MARRIAGE = 'MARRIAGE',

  /** 通用征信授权，获取借款协议，并完成阅读和同意 */
  AGREEMENT_COMMON = 'AGREEMENT_COMMON',

  /** 借款协议，获取借款协议，并完成阅读和同意 */
  AGREEMENT_LOAN = 'AGREEMENT_LOAN',
}

export enum LoanStatusEnum {
  /** 申请中 */
  APPLYING = 1,
  /** 正常还款中 */
  REPAYING = 2,
  /** 已逾期 */
  OVERDUE = 3,
  /** 已结清 */
  CLEARED = 4,
  /** 借款失败 */
  FAILED = 5
}

// 借款申请
export interface LoanRecordDetail {
  // 荣耀方用户id
  userId: string;
  // 荣耀侧借款申请订单号
  applyNo: string;
  /** 渠道方借款申请流水号(已交易鉴权未提交场景无需返回） */
  outOrderNo?: string;
  /** 申请状态
   * 0 - 初始状态（已交易鉴权未提交，CP订单还在有效期）
   * 1 - 审核中
   * 2 - 成功
   * 3 - 失败（申请审核失败）
   * 4 - 拒绝
   * 5 - 取消（放款失败）
   * 6 - 需鉴权（用信补录，补签协议后再提交借款）
   * 7 - (已交易鉴权未提交，CP缓存订单失效）
   */
  applyStatus: LoanApplyStatusEnum;
  /** 借款金额，单位：分 */
  loanAmount: number;

  /**
   * 还款方式
   * 1-等额本息(灵活还)
   * 2-等额本金(灵活还)
   * 3-先息后本(灵活还)
   * 4-等额本息(按期还)
   */
  repayMethod: number;

  /** 借款期数 */
  totalTerm: number;

  /** 绑卡Id */
  bankCardId: string;

  /**
   * 借款用途
   * RCXF - 个人日常消费
   * ZX - 房屋装修
   * LY - 旅游出行
   * JX - 在职深造
   * JKYL - 健康医疗
   * Others - 其他消费
   */
  loanUse: string;

  /** 优惠券id（可选） */
  couponNo?: string;

  /** 联合建模模型分（Map<key,value>的json字符串，可选） */
  apiModelScoreMap?: string;

  /** 用户标签（Map<key,value>的json字符串，可选） */
  apiUserTagMap?: string;
  /** 借款申请时间，毫秒 (已交易鉴权未提交场景无需返回） */
  applyTime?: number;
  /** 拒绝原因码（失败必传） */
  refuseCode?: string;
  /** 拒绝原因说明（失败必传） */
  refuseMsg?: string;
  /** 拒绝具体原因说明（失败必传） */
  refuseMsgData?: string;
  /** 拒绝管控期，单位：天 */
  refuseControlDays?: number;
  /** 需鉴权列表，参照1.4.4借款交易鉴权项
   * （状态是审核中，如返回有鉴权列表，需完成鉴权后再提交借款）
   */
  verifyList?: string[];
  /** 机构名称，多个资方以"&"分割 */
  institutionNames?: string;
}


export interface LoanOrderDetail {
  // 荣耀方用户id
  userId: string;
  /** 荣耀侧借款申请订单号 */
  applyNo: string;
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 借款申请日期，yyyyMMdd */
  applyDate: string;
  /** 借款申请时间，毫秒 */
  applyTime: number;
  /** 借款起息日 */
  effectiveDate?: string;
  /** 借款金额，单位：分 */
  loanAmount: number;
  /** 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败 */
  status: LoanStatusEnum;
  /** 借款期数 */
  totalTerm: number;
  /** 还款方式 */
  repayMethod: number;
  /** 借款来源，appId */
  loanSource?: string;
  /** 机构名称，多个资方以"&"分割 */
  institutionNames?: string;
  /** 结清时间(未结清：0)，毫秒 */
  clearTime: number;
  /** 已还总金额，单位：分 */
  paidAmount: number;
  /** 已还本金总额，单位：分 */
  paidPrinAmount: number;
  /** 已还利息总额，单位：分 */
  paidInterAmount: number;
  /** 已还服务费总额，单位：分 */
  paidServiceFee: number;
  /** 已还罚息，单位：分 */
  paidPenalty: number;
  /** 收款卡号（后四位） */
  bindCardNo: string;
  /** 收款卡发卡行code */
  bindBankCode: string;
  /** 收款卡发卡行名称 */
  bindBankName: string;
  /** 优惠券id */
  couponNo?: string;
  /** 日利率 */
  dayRate?: string;
  /** 月利率 */
  monthRate?: string;
  /** 年利率 */
  apr: string;
  /** 优惠金额，单位：分 */
  reductionAmount: number;
  /** 提前还款违约金，单位：分 */
  prePenalty: number;
  /** 还款计划列表，借款失败可不传 */
  repayPlanTerms?: RepayPlanTermDto[];
  /** 已签署借款协议列表，借款失败可不传 */
  contractList?: ContractDto[];
  /** 借据逾期天数，有逾期必传 */
  overdueDays?: number;
  /** 借据逾期未还金额，有逾期必传 */
  overdueAmount?: number;
}

export interface ContractDto {
  /** 协议ID */
  contractId: string;
  /** 合同名称 */
  contractName: string;
  /** 合同地址 */
  contractUrl: string;
}

export interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 当期状态 1-已结清，2-待还款，3-部分已还，4-逾期，5-宽限期 */
  termStatus: number;
  /** 还款类型 1-主动还款，2-银行卡代扣 */
  repayCategory?: number;
  /** 本期应还总额,单位：分 */
  termAmount: number;
  /** 本期应还本金，单位：分 */
  termPrincipal: number;
  /** 本期应还利息，单位：分 */
  termInterest: number;
  /** 本期应还罚息，单位：分 */
  termPenalty: number;
  /** 本期应还本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期应还利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期应还逾期费，单位：分 */
  termOverdueFee: number;
  /** 本期应还服务费，单位：分 */
  termServiceFee: number;
  /** 本期应还违约金，单位：分 */
  termViolateFee: number;
  /** 本期优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期实际还款日，yyyyMMdd，有还款时必传 */
  paidTime?: number;
  /** 本期实还总额，单位：分，有还款时必传 */
  paidTermAmount?: number;
  /** 本期实还本金，单位：分，有还款时必传 */
  paidTermPrincipal?: number;
  /** 本期实还利息，单位：分，有还款时必传 */
  paidTermInterest?: number;
  /** 本期实还优惠券额，单位：分，有还款时必传 */
  paidTermReductionAmount?: number;
  /** 本期实还罚息，单位：分，有还款时必传 */
  paidTermPenalty?: number;
  /** 本期实还本金罚息，单位：分，有还款时必传 */
  paidTermPrinPenalty?: number;
  /** 本期实还利息罚息，单位：分，有还款时必传 */
  paidTermInterPenalty?: number;
  /** 本期实还逾期费，单位：分，有还款时必传 */
  paidTermOverdueFee?: number;
  /** 本期实还服务费，单位：分，有还款时必传 */
  paidTermServiceFee?: number;
  /** 本期实还违约金，单位：分，有还款时必传 */
  paidTermViolateFee?: number;
  /** 剩余应还还款金额，单位：分，有待还时必传 */
  payableTermAmount?: number;
  /** 剩余应还还款本金，单位：分，有待还时必传 */
  payableTermPrincipal?: number;
  /** 剩余应还还款利息，单位：分，有待还时必传 */
  payableTermInterest?: number;
  /** 剩余应还罚息，单位：分，有待还时必传 */
  payableTermPenalty?: number;
  /** 剩余应还本金罚息，单位：分，有待还时必传 */
  payableTermPrinPenalty?: number;
  /** 剩余应还利息罚息，单位：分，有待还时必传 */
  payableTermInterPenalty?: number;
  /** 剩余应还逾期费，单位：分，有待还时必传 */
  payableTermOverdueFee?: number;
  /** 剩余应还服务费，单位：分，有待还时必传 */
  payableTermServiceFee?: number;
  /** 剩余应还违约金，单位：分，有待还时必传 */
  payableTermViolateFee?: number;
  /** 逾期天数，有逾期必传 */
  overdueDays?: number;
  /** 逾期金额，单位：分，有逾期必传 */
  overdueAmount?: number;
  /** 是否提前还款 */
  preRepay?: boolean;
  /** 是否逾期 */
  overdue: boolean;
  /** 优惠信息说明 */
  reductionAmountDesc?: string;
}



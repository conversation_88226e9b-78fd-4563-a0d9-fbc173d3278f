import prisma from "@/utils/prisma";
import { ReofferInfo, RepayResultStatuses } from "@/types";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const reofferInfo = scene.reofferInfo as ReofferInfo;

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      repayStatus: scene.repayResultStatus || RepayResultStatuses.SUCCESS,
      supprtReoffer: reofferInfo?.supprtReoffer,
    },
  };

  return Response.json(responseBody);
}

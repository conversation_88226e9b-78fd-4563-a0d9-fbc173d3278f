"use client";
import {
  Button,
  NumberInput,
  Select,
  SelectItem,
  Input,
  Spacer,
  Tooltip, DatePicker, addToast,
} from "@heroui/react";
import { createScene, getAtByCode, getScenesByUserId } from "@/actions/sceneActions";
import React, { useEffect, useState } from "react";
import LoanOrderTable from "../../../../components/loanOrder/LoanOrderTable";
import { state } from "sucrase/dist/types/parser/traverser/base";
import { Scene } from "@/types";

let result: "";

export default function SceneCreatePage() {
  const [code, setCode] = useState("");
  const [result, setResult] = useState("");
  // let code: "";

  const submit = async () => {
    // 直接调用 Server Action
    const response = await getAtByCode(code);

    // 检查 Server Action 的返回结果
    if (response) {
      setResult(response);
      addToast({
        title: "获取成功",
        color: "success"
      });
    }
  };

  const handleCopyAT = () => {
    navigator.clipboard.writeText(result);
    alert('已复制到剪贴板');
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText("https://hnoauth-login-test-drcn.cloud.honor.com/oauth2/v3/authorize?response_type=code&client_id=220619456&redirect_uri=honorid://redirect_url&scope=openid%20profile&access_type=offline&state=abcd4321&nonce=abcd4321&display=page");
    alert('已复制获取Code链接到剪贴板');
  };

  return (
    <div className="px-4 py-2 w-full h-full">
      <div className="flex flex-row gap-2 items-center">
        <div className="text-2xl font-bold">根据Code获取AT</div>
      </div>

      <Spacer y={4} />

      <button onClick={handleCopyCode} className="p-2 bg-blue-500 text-white rounded">
        获取Code链接
      </button>

      <br/>
      <br/>
      <br/>

      <Input
        isRequired
        className={"mb-4"}
        label="Code"
        labelPlacement="outside"
        name="Code"
        placeholder="请输入Code"
        onValueChange={(value) => setCode(value)}
      />

    <br/>
    <br/>

      <button
        className="p-2 bg-blue-500 text-white rounded"
        onClick={submit}
      >
        获取AT
      </button>

      <br/>
      <br/>
      <br/>

      <textarea
        readOnly
        placeholder="AT值是"
        value={result}
        className="w-full p-2 border rounded mb-4"
      />

      <br/>
      <br/>
      <br/>
      <button onClick={handleCopyAT} className="p-2 bg-blue-500 text-white rounded">
        复制AT
      </button>
    </div>
  );
}

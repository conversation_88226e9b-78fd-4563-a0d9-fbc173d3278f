"use client";
import {
  Dropdown,
  DropdownItem,
  Dropdown<PERSON><PERSON>u,
  Dropdown<PERSON>rigger,
  ScrollShadow,
  User,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import React from "react";

import useSceneStore from "@/store/scene";
import { useUserStore } from "@/store/user";
import SiderItems from "@/app/pages/cp/SiderItems";
import {usePathname} from "next/navigation";

export const items: SidebarItem[] = [
  {
    key: "home",
    href: "/pages/cp/loan-user/all",
    icon: "solar:home-2-linear",
    title: "所有准入用户",
  },
  // {
  //   key: "smallTools",
  //   href: "pages/cp/small-tools",
  //   icon: "solar:users-group-two-rounded-outline",
  //   title: "小工具",
  // },
  {
    key: "smallTools",
    href: "/pages/cp/small-tools",
    icon: "iconoir:tools",
    title: "小工具",
  },
];

export enum SidebarItemType {
  Nest = "nest",
}

export type SidebarItem = {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  type?: SidebarItemType.Nest;
  startContent?: React.ReactNode;
  endContent?: React.ReactNode;
  items?: SidebarItem[];
  className?: string;
};
export default function CpSidebar() {
  useSceneStore((state) => state.setScenes);
  const logout = useUserStore((state) => state.logoutUser);
  const user = useUserStore((state) => state.currentUser);
  const pathname = usePathname();
  const defaultSelectedKey = items.find(item => item.href === pathname)?.key || "home";

  // 退出登录的处理
  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  return (
    <div className="flex flex-col gap-2 items-center w-56 min-h-screen bg-gray-800 text-white">
      <div className="flex items-center w-full py-2 px-2">
        <Dropdown placement="bottom-start">
          <DropdownTrigger>
            <User
              as="button"
              className="rounded-none flex flex-row justify-normal flex-1 bg-transparent transition-transform"
              name={user?.username}
            />
          </DropdownTrigger>
          <DropdownMenu aria-label="User Actions" variant="flat">
            <DropdownItem key="logout" onPress={handleLogout}>
              退出
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Icon
          icon="solar:logout-2-linear"
          className="text-gray-300 hover:text-white cursor-pointer transition-colors"
          width={20}
          height={20}
          onClick={handleLogout}
        />
      </div>

      <ScrollShadow className="w-full h-full max-h-full">
        <SiderItems defaultSelectedKey={defaultSelectedKey} items={items} />
      </ScrollShadow>
    </div>
  );
}

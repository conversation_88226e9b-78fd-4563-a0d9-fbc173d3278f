"use client";

import React, { useEffect, useState } from "react";
import {
  Accordion,
  AccordionItem,
  Button,
  Card,
  CardBody,
  Input,
  NumberInput,
  Select,
  SelectItem,
  Tab,
  Tabs,
} from "@heroui/react";
import { useParams } from "next/navigation";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/cjs/styles/prism";

import { InterfaceLog, LoanUser, loanUserStatusNameMap } from "@/types/cp/loanUser";
import { getLoanUserById, updateLoanUser } from "@/actions/cp/loanUserActions";
import {
  CreditApplyStatusEnum,
  creditApplyStatusNameMap,
} from "@/app/cp/creditApply/type";
import ProductInfoTable from "@/components/cp/productInfo/productInfoTable";
import { getUserInterfaceLogs } from "@/actions/cp/interfaceLogAction";
import { Link } from "@heroui/link";
import { BankCardBindEnum, cardBandStatusNameMap } from "@/types/cp/bankCard";
import BankCardListTable from "@/components/cp/bankCard/BankCardListTable";

export default function Page() {
  const [loanUser, setLoanUser] = useState<LoanUser>();
  const { id } = useParams();

  useEffect(() => {
    const fetchData = async () => {
      const loanUser = await getLoanUserById(parseInt(id as string));

      setLoanUser(loanUser);
    };

    fetchData();
  }, []);

  const submit = async () => {
    const updateRes = await updateLoanUser(loanUser as LoanUser);

    if (updateRes?.error) {
      alert(updateRes?.error);
    } else {
      alert("保存成功");
    }
  };

  const [logs, setLogs] = useState<InterfaceLog[]>([]);
  const tabSelectionChange = async (key: React.Key) => {
    if (key === "interfaceLog") {
      if (!loanUser?.id) {
        return
      }
      const logs = await getUserInterfaceLogs(loanUser?.id);

      setLogs(logs);
    }
  }

  return (
    <>
      <Tabs aria-label="Options" isVertical={true} size={"lg"}
        onSelectionChange={tabSelectionChange}>
        <Tab key="credit" className={"w-full"} title="授信">
          <Card>
            <CardBody>
              <Accordion defaultExpandedKeys={["action"]} variant="shadow">
                <AccordionItem
                  key="action"
                  aria-label="状态模拟"
                  title="状态模拟"
                >
                  <div className={"grid grid-cols-4 gap-4 mb-4"}>
                    <Select
                      isDisabled={
                        loanUser?.creditInfo?.status?.applyStatus !== 0
                      }
                      label="授信提交结果"
                      selectedKeys={[
                        loanUser?.creditInfo?.actions?.creditApplyStatus.toString(),
                      ]}
                      onSelectionChange={(key) => {
                        setLoanUser({
                          ...loanUser,
                          creditInfo: {
                            ...loanUser?.creditInfo,
                            actions: {
                              creditApplyStatus: parseInt(
                                key.anchorKey as string,
                              ),
                            },
                          },
                        });
                      }}
                    >
                      {Array.from(creditApplyStatusNameMap.keys()).map(
                        (status: number) => (
                          <SelectItem key={status}>
                            {creditApplyStatusNameMap.get(status)}
                          </SelectItem>
                        ),
                      )}
                    </Select>

                    {loanUser?.creditInfo?.actions?.creditApplyStatus ===
                    CreditApplyStatusEnum.PASSED ? (
                      <NumberInput
                        isDisabled={
                          loanUser?.creditInfo?.status?.applyStatus !== 0
                        }
                        label="授信总额度(单位：分)"
                        minValue={10000}
                        step={10000}
                        value={loanUser?.creditInfo?.actions?.creditLimit}
                        onValueChange={(value) => {
                          setLoanUser({
                            ...loanUser,
                            creditInfo: {
                              ...loanUser?.creditInfo,
                              actions: {
                                ...loanUser?.creditInfo?.actions,
                                creditLimit: value,
                              },
                            },
                          });
                        }}
                      />
                    ) : (
                      ""
                    )}
                  </div>

                  {loanUser?.creditInfo?.actions?.creditApplyStatus ===
                  CreditApplyStatusEnum.PASSED ?
                    <ProductInfoTable
                      productInfos={
                        loanUser?.creditInfo?.status?.productInfos || []
                      }
                      setProductInfos={(productInfos) => {
                        setLoanUser({
                          ...loanUser,
                          creditInfo: {
                            ...loanUser?.creditInfo,
                            status: {
                              ...loanUser?.creditInfo?.status,
                              productInfos: productInfos,
                            },
                          },
                        });
                      }}
                    />: ""}
                </AccordionItem>

                <AccordionItem
                  key="state"
                  aria-label="当前状态"
                  title="当前状态"
                >
                  <Input
                    isDisabled
                    className="max-w-xs mb-4"
                    label="授信状态"
                    value={
                      loanUserStatusNameMap[
                        loanUser?.creditInfo?.status?.applyStatus
                      ]
                    }
                  />
                </AccordionItem>
              </Accordion>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="bankCard" className={"w-full"} title="银行卡">
          <Card>
            <CardBody>
              <Accordion defaultExpandedKeys={["action"]} variant="shadow">
                <AccordionItem
                  key="action"
                  aria-label="状态模拟"
                  title="状态模拟(需绑定银行卡前配置)"
                >
                  <div className={"grid grid-cols-4 gap-4 mb-4"}>
                    <Select
                      label="银行卡绑定结果"
                      selectedKeys={[
                        loanUser?.creditInfo?.actions?.creditApplyStatus.toString(),
                      ]}
                      onSelectionChange={(key) => {
                        setLoanUser({
                          ...loanUser,
                          creditInfo: {
                            ...loanUser?.creditInfo,
                            actions: {
                              creditApplyStatus: parseInt(
                                key.anchorKey as string,
                              ),
                            },
                          },
                        });
                      }}
                    >
                      {Array.from(cardBandStatusNameMap.keys()).map(
                        (status: number) => (
                          <SelectItem key={status}>
                            {cardBandStatusNameMap.get(status)}
                          </SelectItem>
                        ),
                      )}
                    </Select>
                  </div>
                </AccordionItem>

                <AccordionItem
                  key="state"
                  aria-label="当前状态"
                  title="当前状态"
                >
                  <BankCardListTable bankCardList={loanUser?.bankCardInfo?.status?.bankCardList.filter(bankCard => bankCard.status === BankCardBindEnum.BOUND)} />
                </AccordionItem>
              </Accordion>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="loan" className={"w-full"} title="用信">
          <Card>
            <CardBody>
              <Accordion defaultExpandedKeys={["action"]} variant="shadow">
                <AccordionItem
                  key="action"
                  aria-label="状态模拟"
                  title="状态模拟(需借款前配置)"
                >
                  <div className={"grid grid-cols-4 gap-4 mb-4"}>
                    <Select
                      label="银行卡绑定结果"
                      selectedKeys={[
                        loanUser?.creditInfo?.actions?.creditApplyStatus.toString(),
                      ]}
                      onSelectionChange={(key) => {
                        setLoanUser({
                          ...loanUser,
                          creditInfo: {
                            ...loanUser?.creditInfo,
                            actions: {
                              creditApplyStatus: parseInt(
                                key.anchorKey as string,
                              ),
                            },
                          },
                        });
                      }}
                    >
                      {Array.from(cardBandStatusNameMap.keys()).map(
                        (status: number) => (
                          <SelectItem key={status}>
                            {cardBandStatusNameMap.get(status)}
                          </SelectItem>
                        ),
                      )}
                    </Select>
                  </div>
                </AccordionItem>

                <AccordionItem
                  key="state"
                  aria-label="当前状态"
                  title="当前状态"
                >
                  <ProductInfoTable
                    productInfos={
                      loanUser?.creditInfo?.status?.productInfos || []
                    }
                    setProductInfos={(productInfos) => {
                      setLoanUser({
                        ...loanUser,
                        creditInfo: {
                          ...loanUser?.creditInfo,
                          status: {
                            ...loanUser?.creditInfo?.status,
                            productInfos: productInfos,
                          },
                        },
                      });
                    }}
                  />
                </AccordionItem>
              </Accordion>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="interfaceLog" className={"w-full"} title="接口日志">
          <Card>
            <CardBody>
              <Accordion>
                {logs.map((log) => (
                  <AccordionItem
                    key={log.id}
                    aria-label={log.interfaceName}
                    title={'接口名称：' + log.interfaceName + ' 请求时间： ' + log.createTime.toLocaleString()}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <Card>
                        <CardBody>
                          <div className="mb-2">
                            <span className="font-bold text-lg">请求数据</span>
                          </div>
                          <pre className="p-4 rounded-lg overflow-auto max-h-[500px]">
                            <SyntaxHighlighter language="json" style={oneDark}>
                              {JSON.stringify(JSON.parse(log.requestData), null, 2)}
                            </SyntaxHighlighter>
                          </pre>
                        </CardBody>
                      </Card>
                      <Card>
                        <CardBody>
                          <div className="mb-2">
                            <span className="font-bold text-lg">响应数据</span>
                          </div>
                          <pre className="p-4 rounded-lg overflow-auto max-h-[500px]">
                            <SyntaxHighlighter language="json" style={oneDark}>
                              {JSON.stringify(JSON.parse(log.responseData), null, 2)}
                            </SyntaxHighlighter>
                          </pre>
                        </CardBody>
                      </Card>
                    </div>
                    <div className={"flex items-center mt-4 gap-4"}>
                      <div className="text-sm text-gray-500">
                        创建时间: {log.createTime.toLocaleString()}
                      </div>
                      <Link size={"sm"} href="https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#part-DpNOdui6Vo2y5ox8QO2lMoJGr1f">前往接口文档</Link>
                    </div>

                  </AccordionItem>
                ))}
              </Accordion>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>

      <Button
        className={"fixed bottom-10 right-10"}
        color="primary"
        size={"lg"}
        type="submit"
        onPress={submit}
      >
        保存
      </Button>
    </>
  );
}

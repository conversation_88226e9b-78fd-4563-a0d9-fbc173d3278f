"use client";

import React, { useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
} from "@heroui/react";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";

import { LoanOrder } from "@/types";
import { getAllLoanUsers, getMyLoanUsers } from "@/actions/cp/loanUserActions";
import { LoanUser } from "@/types/cp/loanUser";
import { RightIcon } from "@/components/icons";
import { useUserStore } from "@/store/user";

const columns = [
  { name: "姓名", uid: "realName" },
  { name: "身份证", uid: "ctfCode" },
  { name: "手机号", uid: "mobileNo" },
  { name: "userId", uid: "userId" },
  { name: "创建时间", uid: "createTime" },
  { name: "操作", uid: "actions" },
];

export default function Page(props: {
  className: string;
  loanOrders?: LoanOrder[];
  setLoanOrders: (loanOrders: LoanOrder[]) => void;
}) {
  const [allLoanUsers, setAllLoanUsers] = React.useState<LoanUser[]>([]);
  const router = useRouter();
  const user = useUserStore((state) => state.currentUser);



  useEffect(() => {
    getMyLoanUsers(user?.id).then((res: LoanUser[]) => {
      setAllLoanUsers(res);
    });
  }, []);

  const renderCell = React.useCallback((user: any, columnKey: any) => {
    const cellValue = user[columnKey];

    switch (columnKey) {
      case "createTime":
        // 将 BigInt 转换为字符串，然后转换为数字
        return dayjs(Number(cellValue.toString())).format(
          "YYYY-MM-DD HH:mm:ss",
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip content="进入">
              <span
                className="text-lg text-default-400 cursor-pointer active:opacity-50"
                onClick={() => {
                  router.push(`/pages/cp/loan-user/${user.id}`);
                }}
              >
                <RightIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>我的准入账号</div>
    </div>
  );

  return (
    <>
      <Table className={props.className} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={allLoanUsers}>
          {(user) => (
            <TableRow key={user.id}>
              {(columnKey) => (
                <TableCell>{renderCell(user, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}

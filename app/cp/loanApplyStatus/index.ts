import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId, updateLoanUser } from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { BankCardInfo } from "@/types/cp/bankCard";
import { LoanApplyStatusEnum, LoanInfo, LoanOrderDetail } from "@/types/cp/loanInfo";
import { CreditApplyStatusEnum } from "@/types/cp/creditInfo";

const interfaceName = "loan.apply.status";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;

  /** 荣耀侧借款申请订单号 */
  applyNo: string;
}

// 响应数据结构
export interface ResponseDataType {
  /** 渠道方借款申请流水号 (已交易鉴权未提交场景无需返回) */
  outOrderNo?: string;

  /**
   * 申请状态
   * 0 - 初始状态（已交易鉴权未提交，CP订单还在有效期）
   * 1 - 审核中
   * 2 - 成功
   * 3 - 失败（申请审核失败）
   * 4 - 拒绝
   * 5 - 取消（放款失败）
   * 6 - 需鉴权（用信补录，补签协议后再提交借款）
   * 7 - 已交易鉴权未提交，CP缓存订单失效
   */
  applyStatus: LoanApplyStatusEnum;

  /** 借款申请时间，毫秒 (已交易鉴权未提交场景无需返回) */
  applyTime?: number;

  /** 拒绝原因码（失败必传） */
  refuseCode?: string;

  /** 拒绝原因说明（失败必传） */
  refuseMsg?: string;

  /** 拒绝具体原因说明（失败必传） */
  refuseMsgData?: string;

  /** 拒绝管控期，单位：天 */
  refuseControlDays?: number;

  /**
   * 需鉴权列表，参照1.4.4借款交易鉴权项
   * （状态是审核中，如返回有鉴权列表，需完成鉴权后再提交借款）
   */
  verifyList?: string[];

  /**
   * 机构名称，多个资方以"&"分割
   */
  institutionNames?: string;
}


export async function loanApplyStatus(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  const loanApplyRecord = loanInfo.status?.loanApplyRecords?.find((item) => {
    return item.applyNo === params.applyNo;
  });

  if (!loanApplyRecord) {
    console.log("订单不存在");

    return encryptRes({
      code: 1,
      message: "订单不存在",
      data: null,
    });
  }

  responseData.applyStatus = loanApplyRecord.applyStatus;

  if (loanApplyRecord.applyStatus === LoanApplyStatusEnum.SUCCESS) {
    responseData.outOrderNo = loanApplyRecord.outOrderNo;
  }

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}

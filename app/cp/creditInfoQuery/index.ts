import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { CreditApplyStatusEnum, CreditInfo } from "@/types/cp/creditInfo";

const interfaceName = "credit.info.query";

enum StatusEnum {
  NORMAL = 1,
  PENDING = 2,
  OVERDUE = 3,
  REJECTED = 4,
}

export interface ParamType {
  userId: string;
}

export interface ResponseDataType {
  status: StatusEnum;
  creditLimit: number;
  limitType: number;
  remainLimit: number;
  limitExpireDate: string;
  repayDay: string;
  limitUseErrStatus: number;
  limitUseErrDesc: string;
  greyExpireTime: number;
  totalAvailableLimit: number;
  totalCreditLimit: number;
  maxLoan: number;
  minLoan: number;
  canCreditChange: boolean;
  productInfos: ProductInfo[];
  repayInfo: RepayInfo;
  tempLimitInfo: TempLimitInfo;
  overdueInfo: OverdueInfo;
  applyTime: number;
}

export interface ProductInfo {
  dayRate: string;
  monthRate?: string;
  apr: string;
  repayMethod: RepayMethod;
  earlyRepay: boolean;
  termNums: number[];
  termPriceInfos?: TermPriceInfoDto[];
  tempDayRate?: string;
  tempApr?: string;
  tempPriceDueTime?: string;
}

/** 还款方式 */
export enum RepayMethod {
  /** 1-等额本息 */
  EqualPrincipalAndInterest = 1,
  /** 2-等额本金 */
  EqualPrincipal = 2,
  /** 3-先息后本 */
  InterestFirst = 3,
  /** 4-锁期（固定期限） */
  FixedTerm = 4,
}

export interface TermPriceInfoDto {
  termNum: number;
  dayRate: string;
  monthRate: string;
  apr: string;
  tempDayRate: string;
  tempApr: string;
  tempPriceDueTime: string;
}

export interface RepayInfo {
  totalAmount: number;
  principal: number;
  interest: number;
  serviceFee: number;
}

export interface TempLimitInfo {
  tempLimitValidTime: string;
  tempCreditLimit: number;
  tempAvailableLimit: number;
}

export interface OverdueInfo {
  overdueAmount: number;
  overdueOrder: number;
  overdueDays: number;
  overduePenalty: number;
}

export async function creditInfoQuery(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.REJECTED) {
    responseData.status = StatusEnum.REJECTED;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PENDING) {
    responseData.status = StatusEnum.PENDING;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PASSED) {
    responseData.status = StatusEnum.NORMAL;
    responseData.creditLimit = creditInfo?.status?.creditLimit;
    responseData.remainLimit = creditInfo?.status?.remainLimit;
    responseData.totalAvailableLimit = creditInfo?.status?.remainLimit;
    responseData.totalCreditLimit = creditInfo?.status?.creditLimit;
    responseData.maxLoan = creditInfo?.status?.maxLoan || 20000000;
    responseData.minLoan = creditInfo?.status?.minLoan || 50000;
    responseData.canCreditChange = creditInfo?.status?.canCreditChange || false;

    responseData.productInfos = creditInfo?.status?.productInfos;
    responseData.applyTime = creditInfo?.status?.applyTime;
  }
  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })

  return encryptRes(res);
}

import {encryptRes} from "@/utils/decrypt";
import {getLoanUserByUserId, updateLoanUser} from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import {CreditApplyStatusEnum} from "@/types/cp/creditInfo";

const interfaceName = "credit.apply.status";

export interface ParamType {
  userId: string;
  applyNo: string;
}

export interface ResponseDataType {
  outOrderNo: string
  applyStatus: number
  refuseCode: string
  refuseMsg: string
  refuseMsgData: string
  refuseControlTime: number
  identity: number
  creditLimit: number
  originCreditLimit: number
  limitType: number
  remainLimit: number
  limitExpireDate: string
  greyExpireTime: number
  totalAvailableLimit: number
  totalCreditLimit: number
  dayRate: string
  monthRate: string
  apr: string
  maxLoan: number
  minLoan: number
  tempLimitInfo: TempLimitInfo
  applyTime: number
}

interface TempLimitInfo {
  tempLimitValidTime:string
  tempCreditLimit: number
  tempAvailableLimit: number
  tempPriceValidTime: string
  tempDayRate: string
  tempMonthRate: string
  tempApr: string
}
export async function creditApplyStatus(params:ParamType) {
  console.log('请求入参:', params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log('用户不存在');
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData:ResponseDataType = {};
  if (user.creditInfo?.status?.applyStatus === CreditApplyStatusEnum.REJECTED) {
    responseData.outOrderNo = user.creditInfo?.status?.outOrderNo;
    responseData.applyStatus = user.creditInfo?.status?.applyStatus;
    responseData.refuseCode = user.creditInfo?.status?.refuseCode;
    responseData.refuseMsg = user.creditInfo?.status?.refuseMsg;
    responseData.refuseMsgData = user.creditInfo?.status?.refuseMsgData;
    responseData.applyTime = user.creditInfo?.status?.applyTime;

  }else if (user.creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PENDING) {
    responseData.outOrderNo = user.creditInfo?.status?.outOrderNo;
    responseData.applyStatus = user.creditInfo?.status?.applyStatus;
    responseData.applyTime = user.creditInfo?.status?.applyTime;

  }else if (user.creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PASSED) {
    responseData.outOrderNo = user.creditInfo?.status?.outOrderNo;
    responseData.applyStatus = user.creditInfo?.status?.applyStatus;
    responseData.applyTime = user.creditInfo?.status?.applyTime;
    responseData.creditLimit = user.creditInfo?.status?.creditLimit;
    responseData.remainLimit = user.creditInfo?.status?.remainLimit;
    responseData.totalAvailableLimit = user.creditInfo?.status?.remainLimit;
    responseData.totalCreditLimit = user.creditInfo?.status?.creditLimit;
    responseData.dayRate = user.creditInfo?.status?.dayRate;
    responseData.monthRate = user.creditInfo?.status?.monthRate;
    responseData.apr = user.creditInfo?.status?.apr;
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      ...user?.creditInfo?.status,
    },
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}
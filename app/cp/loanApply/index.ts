import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId, updateLoanUser } from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { BankCardInfo } from "@/types/cp/bankCard";
import { LoanApplyStatusEnum, LoanInfo, LoanOrderDetail, LoanStatusEnum, RepayPlanTermDto } from "@/types/cp/loanInfo";
import { CreditApplyStatusEnum, CreditInfo } from "@/types/cp/creditInfo";
import dayjs from "dayjs";

const interfaceName = "loan.apply";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;

  /** 荣耀侧借款申请订单号 */
  applyNo: string;

  /** 联系人信息，参照1.4.2联系人信息 */
  relationInfos?: RelationInfo[]; // O，交易鉴权返回BASICINFO_FILL或BASICINFO_FILL2必须

  /** 居住地 */
  residentialAddr?: string; // O，交易鉴权返回RESIDENTIAL_ADDR必须

  /** 家庭地址 */
  familyAddr?: string; // O，交易鉴权返回FAMILY_ADDR必须

  /** 教育程度 */
  education?: '1' | '2' | '3' | '4' | '5' | '6'; // O，交易鉴权返回EDUCATION必须
  // 1-研究生或以上 2-本科 3-大专 4-高中 5-中专 6-初中及以下

  /** 工作单位 */
  company?: string; // O，交易鉴权返回COMPANY必须

  /** 职业 */
  career?:
    | '01' // 党政机关/事业单位
    | '02' // 企业职员
    | '03' // 工人/服务人员
    | '04' // 生意人/个体户
    | '05' // 学生
    | '06' // 自由职业
    | '07' // 其他
    | '08' // 律师/会计师等专业技术人员
    | '09' // 金融行业从业人员
    | '10' // 农/林/牧/渔从业人员
  ; // O，交易鉴权返回CAREER_INCOME必须

  /** 收入 */
  income?:
    | '01' // 无稳定收入
    | '02' // 3千以下
    | '03' // 3千-5千
    | '04' // 5千-1万
    | '05' // 1万-2万
    | '06' // 2万-3万
    | '07' // 3万以上
  ; // O，交易鉴权返回CAREER_INCOME必须

  /** 婚姻状态 */
  marriage?: '1' | '2' | '3' | '4' | '5'; // O，交易鉴权返回MARRIAGE必须
  // 1-未婚 2-已婚 3-其他 4-离异 5-丧偶

  /** 文件信息，参照1.4.1文件信息 */
  fileInfos?: FileInfo[]; // O，交易鉴权返回FACE_CHECK或ID_CARD_OCR必须

  /** 联合建模模型分 */
  apiModelScore?: ApiModelScoreParam; // O

  /** 联合建模模型分（Map<key,value>的json字符串） */
  apiModelScoreMap?: string; // O

  /** 用户标签（Map<key,value>的json字符串） */
  apiUserTagMap?: string; // O
}

interface RelationInfo {
  /** 联系人关系 */
  relation: string;

  /** 联系人姓名 */
  name: string;

  /** 联系人手机号 */
  mobileNo: string;
}
interface FileInfo {
  /**
   * 数据类型
   * CARD_FRONT_PHOTO：身份证正面照片
   * CARD_BACK_PHOTO：身份证反面照片
   * FRONT_PHOTO：活体照片
   * FRONT_PHOTO_VIDEO：视频活体照片
   * FRONT_VIDEO：活体视频
   */
  type:
    | 'CARD_FRONT_PHOTO'
    | 'CARD_BACK_PHOTO'
    | 'FRONT_PHOTO'
    | 'FRONT_PHOTO_VIDEO'
    | 'FRONT_VIDEO';

  /**
   * 具体数据
   * 照片、视频采用base64编码或url【base64编码前大小不能超过5M】
   */
  value: string;

  /** 数据标识 */
  identifier?: string;
}

interface ApiModelScoreParam {
  /** 风险分 */
  riskScore: string;

  /** 需求分 */
  demandScore: string;
}

// 响应数据结构
export interface ResponseDataType {
  /** 渠道方借款申请流水号（创建返回订单成功则订单号） */
  outOrderNo?: string;

  /** 拒绝原因码 0-表示申请提交成功 */
  refuseCode: string;

  /** 拒绝原因说明（失败必传） */
  refuseMsg?: string;

  /** 拒绝原因说明（失败必传） */
  refuseMsgData?: string;
}


export async function loanApply(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};


  const applyNo = params.applyNo;
  const loanInfo = user.loanInfo as unknown as LoanInfo;

  const loanApplyRecord = loanInfo.status?.loanApplyRecords?.find((item) => {
    return item.applyNo === applyNo;
  });


  if (!loanApplyRecord) {
    console.log("订单不存在");

    return encryptRes({
      code: 1,
      message: "申请订单不存在",
      data: null,
    });
  }
  const bindCardInfo = user.bankCardInfo as unknown as BankCardInfo;

  const bankCard = bindCardInfo.status?.bankCardList?.find((item) => {
    return item.bankCardId === loanApplyRecord?.bankCardId;
  });

  if (!bankCard) {
    console.log("绑卡信息不存在");

    return encryptRes({
      code: 1,
      message: "绑卡信息不存在",
      data: null,
    });
  }


  const creditInfo = user.creditInfo as unknown as CreditInfo;
  creditInfo.status.remainLimit -= loanApplyRecord.loanAmount;

  const productInfo = creditInfo.status.productInfos.find(productInfo => {
    return productInfo.repayMethod === loanApplyRecord.repayMethod;
  });

  if (!productInfo) {
    console.log("还款方式不支持");

    return encryptRes({
      code: 1,
      message: "还款方式不支持",
      data: null,
    });
  }

  if (loanInfo.action?.loanApplyStatus === LoanApplyStatusEnum.SUCCESS) {
    loanApplyRecord.applyStatus = LoanApplyStatusEnum.SUCCESS;
    const loanOrder: LoanOrderDetail = {
      userId: params.userId,
      applyNo: params.applyNo,
      outOrderNo: crypto.randomUUID(),
      applyDate: dayjs(Date.now()).format("YYYYMMDD"),
      applyTime: Date.now(),
      loanAmount: loanApplyRecord.loanAmount,
      status: LoanStatusEnum.REPAYING,
      totalTerm: loanApplyRecord.totalTerm,
      repayMethod: loanApplyRecord.repayMethod,
      clearTime: 0,
      paidAmount: 0,
      paidPrinAmount: 0,
      paidInterAmount: 0,
      paidServiceFee: 0,
      paidPenalty: 0,
      bindCardNo: bankCard.bankCardNo,
      bindBankCode: bankCard.bankCode || "",
      bindBankName: bankCard.bankName,
      dayRate: productInfo?.dayRate,
      monthRate: productInfo?.monthRate,
      apr: productInfo?.apr,
      reductionAmount: 0,
      prePenalty: 0,
    };

    const repayPlans:RepayPlanTermDto[] = [];
    for (let i = 0; i < loanApplyRecord.totalTerm; i++) {
      const termNo = i + 1;
      const shouldRepayDate = dayjs(Date.now()).add(i, "month").format("YYYYMMDD");
      const termInterest = Math.round(
        (loanApplyRecord.loanAmount * productInfo?.monthRate) / loanApplyRecord.totalTerm,
      );

      repayPlans.push({
        termNo,
        shouldRepayDate,
        termStatus: 2,
        termAmount: Math.round(
          loanApplyRecord.loanAmount / loanApplyRecord.totalTerm + termInterest,
        ),
        termPrincipal: Math.round(
          loanApplyRecord.loanAmount / loanApplyRecord.totalTerm,
        ),
        termInterest,
        termPenalty: 0,
        termPrinPenalty: 0,
        termInterPenalty: 0,
        termOverdueFee: 0,
        termServiceFee: 0,
        termViolateFee: 0,
        termReductionAmount: 0,
        overdue: false,
      });
    }
    loanInfo.status.loanOrders.push(loanOrder);

    loanApplyRecord.outOrderNo = loanOrder.outOrderNo;
    loanApplyRecord.applyStatus = LoanApplyStatusEnum.SUCCESS;

    responseData.outOrderNo = loanOrder.outOrderNo;
  }


  updateLoanUser(user);

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}

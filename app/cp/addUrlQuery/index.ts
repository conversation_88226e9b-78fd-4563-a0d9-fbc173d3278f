import prisma from "@/utils/prisma";
import {encryptRes} from "@/utils/decrypt";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";

const interfaceName = "credit.addUrl.query";
export interface ParamType {
  userId: string;
}
export async function creditAddUrlQuery(params:ParamType) {
  console.log('请求入参:', params);

  const res = {
    code: 0,
    message: "success",
    data: {
      url: "",
      validTime: ""
    },
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}
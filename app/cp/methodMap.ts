import { userBind } from "@/app/cp/userBind";
import { contractQuery } from "@/app/cp/contractQuery";
import { creditApply } from "@/app/cp/creditApply";
import { creditInfoQuery } from "@/app/cp/creditInfoQuery";
import { creditAddUrlQuery } from "@/app/cp/addUrlQuery";
import { couponList } from "@/app/cp/couponList";
import { repayPlan } from "@/app/cp/repayPlan";
import { creditApplyStatus } from "@/app/cp/creditApplyStatus";
import { loanTrial } from "@/app/cp/loanTrial";
import { bankCardList } from "@/app/cp/bankCardList";
import { bankCardBind } from "@/app/cp/bankCardBind";
import { bankCardSmsVerify } from "@/app/cp/bankCardSmsVerify";
import { loanVerify } from "@/app/cp/loanVerify";
import { loanApply } from "@/app/cp/loanApply";
import { loanApplyStatus } from "@/app/cp/loanApplyStatus";

export const methodMap = {
  // 用户准入
  "user.bind": userBind,
  // 协议查询
  "contract.query": contractQuery,
  // 授信提交
  "credit.apply": creditApply,
  // 用户额度查询
  "credit.info.query": creditInfoQuery,
  // 增信H5链接
  "credit.addUrl.query": creditAddUrlQuery,
  // 优惠券列表
  "coupon.list": couponList,
  // 还款计划查询
  "repay.plan": repayPlan,
  // 授信结果查询
  "credit.apply.status": creditApplyStatus,
  // 借款试算
  "loan.trial": loanTrial,
  // 银行卡列表
  "bankcard.list": bankCardList,
  // 银行卡绑定
  "bankcard.bind": bankCardBind,
  // 银行卡绑定短信验证
  "bankcard.sms.verify": bankCardSmsVerify,
  // 交易鉴权
  "loan.verify": loanVerify,
  // 借款申请
  "loan.apply": loanApply,
  // 借款申请结果查询
  "loan.apply.status": loanApplyStatus,
};

export type MethodName = keyof typeof methodMap;

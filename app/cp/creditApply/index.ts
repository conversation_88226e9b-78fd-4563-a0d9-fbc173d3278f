import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId, updateLoanUser } from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { CreditApplyStatusEnum, CreditInfo } from "@/types/cp/creditInfo";

const interfaceName = "credit.apply";

interface ParamType {
  userId: string;
  applyNo: string;
}

interface ResponseDataType {
  applyStatus: number;
  refuseCode: string;
  refuseMsg: string;
  refuseMsgData: string;
  outOrderNo: string;
  remainLimit: number;
  apr: string;
  dayRate: string;
}

export async function creditApply(params: ParamType) {
  console.log("请求入参:", params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  if (
    creditInfo.actions.creditApplyStatus === CreditApplyStatusEnum.REJECTED
  ) {
    creditInfo.status.applyStatus = CreditApplyStatusEnum.REJECTED;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.refuseCode = "1001";
    creditInfo.status.refuseMsg = "测试拒绝";
    creditInfo.status.refuseMsgData = "测试拒绝";
    creditInfo.status.applyTime = Date.now();

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
    responseData.refuseCode = creditInfo?.status?.refuseCode;
    responseData.refuseMsg = creditInfo?.status?.refuseMsg;
    responseData.refuseMsgData = creditInfo?.status?.refuseMsgData;
  } else if (
    creditInfo?.actions?.creditApplyStatus ===
    CreditApplyStatusEnum.PENDING
  ) {
    creditInfo.status.applyStatus = CreditApplyStatusEnum.PENDING;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.applyTime = Date.now();

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
  } else {
    creditInfo.status.applyStatus = CreditApplyStatusEnum.PASSED;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.creditLimit = creditInfo?.actions?.creditLimit;
    creditInfo.status.remainLimit = creditInfo?.actions?.creditLimit;
    creditInfo.status.maxLoan = 20000000;
    creditInfo.status.minLoan = 50000;
    creditInfo.status.apr = "24.0001";
    creditInfo.status.monthRate = "0.003";
    creditInfo.status.dayRate = "0.0001";
    creditInfo.status.canCreditChange = false;
    creditInfo.status.applyTime = Date.now();

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
    responseData.remainLimit = creditInfo?.status?.remainLimit;
    responseData.apr = creditInfo?.status?.apr;
    responseData.dayRate = creditInfo?.status?.dayRate;
  }

  await updateLoanUser(user);

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}

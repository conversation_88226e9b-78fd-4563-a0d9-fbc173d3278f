import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import {CreditInfo} from "@/types/cp/creditInfo";

const interfaceName = "loan.trial";
export async function loanTrial(params: ParamType) {
  console.log("请求入参:", params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  responseData.shouldRepayPrinAmount = params.loanAmount;

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  const productInfo = creditInfo.status.productInfos.find((item) => {
    return item.repayMethod === params.repayMethod;
  });

  if (!productInfo) {
    return encryptRes({
      code: 1,
      message: "不支持的还款方式",
      data: null,
    });
  }

  const monthRate = parseFloat(productInfo.apr) / (12 * 100); // 月利率

  responseData.shouldRepayInterAmount = Math.round(
    params.loanAmount * monthRate * params.totalTerm,
  );

  responseData.shouldRepayAmount =
    responseData.shouldRepayPrinAmount + responseData.shouldRepayInterAmount;

  // 首次还款日设置为一个月后，格式为 yyyyMMdd
  responseData.firstRepayDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    .toISOString()
    .slice(0, 10)
    .replace(/-/g, "");

  // 最后还款日设置为首次还款日加上总期数，格式为 yyyyMMdd
  responseData.lastRepayDate = new Date(
    Date.now() + 30 * params.totalTerm * 24 * 60 * 60 * 1000,
  )
    .toISOString()
    .slice(0, 10)
    .replace(/-/g, "");

  // 综合年利率
  responseData.annualRate = productInfo.apr;

  // 还款计划
  responseData.repayPlanTerms = Array.from(
    { length: params.totalTerm },
    (_, i) => {
      const termNo = i + 1;
      const shouldRepayDate = new Date(
        Date.now() + 30 * termNo * 24 * 60 * 60 * 1000,
      )
        .toISOString()
        .slice(0, 10)
        .replace(/-/g, "");

      const termInterest = Math.round(
        (params.loanAmount * monthRate) / params.totalTerm,
      );

      return {
        termNo,
        shouldRepayDate,
        termAmount: Math.round(
          params.loanAmount / params.totalTerm + termInterest,
        ),
        termPrincipal: Math.round(params.loanAmount / params.totalTerm),
        termInterest,
        amount: Math.round(params.loanAmount / params.totalTerm + termInterest),
      };
    },
  );
  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}

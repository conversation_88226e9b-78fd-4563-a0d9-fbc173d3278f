import { decrypt, decryptByPrivate<PERSON>ey, verifySign } from "@/utils/decrypt";
import { methodMap, MethodName } from "@/app/cp/methodMap";
export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const requestBody = await req.json();
  const isValid = verifySign(requestBody);

  const key = requestBody.key;

  const decryptKey = decryptByPrivateKey(key);

  const EncryptParams = requestBody.params;

  const decryptParams = JSON.parse(decrypt(EncryptParams, decryptKey));

  const method: MethodName = requestBody.method;

  console.log("请求接口:", method);

  const res = await methodMap[method](decryptParams);

  return Response.json(res);
}

import {encryptRes} from "@/utils/decrypt";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";

const interfaceName = "repay.plan";
export interface ParamType {
  userId: string;
}
export async function repayPlan(params:ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      totalNum: 0,
      records:[
      ]},
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}
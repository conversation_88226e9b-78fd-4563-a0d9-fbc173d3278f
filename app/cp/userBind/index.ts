import { encryptRes } from "@/utils/decrypt";
import { createLoanUser, getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { RepayMethod } from "@/app/cp/creditInfoQuery";
import { CardBandStatusEnum } from "@/types/cp/bankCard";
import { LoanApplyStatusEnum, LoanVerifyItemEnum } from "@/types/cp/loanInfo";
import { CreditApplyStatusEnum } from "@/types/cp/creditInfo";

export interface ParamType {
  userId: string;
  realName: string;
  ctfCode: string;
  mobileNo: string;
}
export async function userBind(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    await createLoanUser({
      ...params,
      creditInfo: {
        status: {
          applyStatus: 0,
          productInfos: [
            {
              dayRate: "0.0005",
              apr: "18.25",
              repayMethod: RepayMethod.EqualPrincipalAndInterest,
              earlyRepay: true,
              termNums: [3, 6, 12],
            },
          ],
        },
        actions: {
          creditApplyStatus: CreditApplyStatusEnum.PASSED,
          creditLimit: 2000000,
        },
      },
      bankCardInfo: {
        status: {
          bankCardList: []
        },
        action: {
          bandStatus: CardBandStatusEnum.PASSED
        }
      },
      loanInfo: {
        status: {
          loanApplyRecords: [],
          loanOrders: []
        },
        action: {
          loanApplyStatus: LoanApplyStatusEnum.SUCCESS,
          verifyList: [LoanVerifyItemEnum.AGREEMENT_LOAN]
        }
      },
      createTime: Date.now(),
    });
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      // 准入状态，1-准入成功，0-准入失败
      access: 1,
      openId: params.userId,
    },
  };

  return encryptRes(res);
}

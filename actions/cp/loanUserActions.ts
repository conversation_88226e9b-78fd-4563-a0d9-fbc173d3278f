"use server";

import prisma from "@/utils/prisma";
import { LoanUser } from "@/types/cp/loanUser";

export async function getAllLoanUsers() {
  return prisma.loan_user.findMany({ orderBy: { createTime: "desc" } });
}

export async function getMyLoanUsers(userId: number) {
  return prisma.loan_user.findMany({
    where: {

    },
    orderBy: { createTime: "desc" },
  });
}

export async function getLoanUserById(id: number) {
  return prisma.loan_user.findUnique({
    where: {
      id,
    },
  });
}

export async function getLoanUserByUserId(userId: string) {
  return prisma.loan_user.findFirst({
    where: {
      userId,
    },
  });
}
export async function createLoanUser(
  loanUser: LoanUser,
): Promise<LoanUser | { error: string }> {
  try {
    return prisma.loan_user.create({
      data: loanUser,
    });
  } catch (error) {
    console.error("创建用户时出错:", error);

    return { error: "创建用户失败，发生意外错误。" };
  }
}

export async function updateLoanUser(
  loanUser: LoanUser,
): Promise<LoanUser | { error: string }> {
  try {
    return prisma.loan_user.update({
      where: {
        id: loanUser.id,
      },
      data: loanUser,
    });
  } catch (error) {
    console.error("更新用户时出错:", error);

    return { error: "更新用户失败，发生意外错误。" };
  }
}

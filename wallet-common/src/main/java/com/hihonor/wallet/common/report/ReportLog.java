/*
 * Copyright (c) Honor Device Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.report;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import com.hihonor.wallet.common.constant.CommonConstant;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.filtertest.FilterTest;
import com.hihonor.wallet.common.model.BaseRequest;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.common.util.log.FilterTestLogs;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.MdcUtil;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Component
@RefreshScope
public class ReportLog {
    /**
     * 成功
     */
    private static final Integer SUCCESS = 0;
    /**
     * 失败
     */
    private static final Integer FAIL = 1;

    @Value("${generalLog.operationRuleNo}")
    private String id;

    @Value("${generalLog.costTimeRuleNo}")
    private String costTimeRuleNo;

    @Value("${spring.application.name}")
    private String serviceName;

    @Autowired
    private FilterTestLogs filterTestLogs;


    /**
     * 成功上报
     *
     * @param event    事件
     * @param cardType 卡片类型
     */
    public void reportSuccess(String event, Integer cardType, Integer deviceType, Integer appType, String... param) {
        FilterTest filterTest = getFilterTest(param);
        if (filterTest != null) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        if (cardType != null) {
            map.put("cardType", cardType);
        }
        if (deviceType != null) {
            map.put("deviceType", deviceType);
        }
        if (appType != null) {
            map.put("appType", appType);
        }
        map.put("result", SUCCESS);
        LogUtil.report(id, serviceName, map);
    }


    /**
     * 失败上报
     *
     * @param event    事件
     * @param cardType 卡片类型
     */
    public void reportFail(String event, Integer cardType, String traceId, String failCode, String failReason,
        String sourceErrorCode, String sourceErrorMsg, Integer deviceType, Integer appType, String... param) {
        FilterTest filterTest = getFilterTest(param);
        if (filterTest != null) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        if (cardType != null) {
            map.put("cardType", cardType);
        }
        if (deviceType != null) {
            map.put("deviceType", deviceType);
        }
        map.put("result", FAIL);
        if (StringUtils.isNotBlank(traceId)) {
            map.put("traceId", traceId);
        }

        if (StringUtils.isNotBlank(failCode)) {
            map.put("failCode", failCode);
        }

        if (StringUtils.isNotBlank(failReason)) {
            map.put("failReason", failReason);
        }

        if (StringUtils.isNotBlank(sourceErrorCode)) {
            map.put("sourceFailCode", sourceErrorCode);
        }

        if (StringUtils.isNotBlank(sourceErrorMsg)) {
            map.put("sourceFailReason", sourceErrorMsg);
        }

        if (appType != null) {
            map.put("appType", appType);
        }
        LogUtil.report(id, serviceName, map);
    }

    /**
     * 成功上报
     *
     * @param event    事件
     * @param cardType 卡片类型
     */
    public void bankReportSuccess(String event, BaseRequest baseRequest, String... param) {
        FilterTest filterTest = getFilterTest(param);
        if (filterTest != null) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        map.put("result", SUCCESS);
        map.put("deviceType", baseRequest.getDeviceType());
        map.put("cplc", getReqCplc(baseRequest));
        LogUtil.report(id, serviceName, map);
    }

    /**
     * 失败上报
     *
     * @param event    事件
     * @param cardType 卡片类型
     */
    public void bankReportFail(String event, BusinessException businessException, BaseRequest baseRequest, String... param) {
        FilterTest filterTest = getFilterTest(param);
        if (filterTest != null) {
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        map.put("result", FAIL);
        map.put("deviceType", baseRequest.getDeviceType());
        map.put("cplc", getReqCplc(baseRequest));
        map.put("failReason", businessException.getErrorMessage());
        map.put("unionResCode", businessException.getExternalErrorCode());
        LogUtil.report(id, serviceName, map);
    }

    /**
     * sp-tsm 埋点上报
     *
     * @param taskName  taskName
     * @param isSuccess isSuccess
     * @param startTime startTime
     */
    public void report(String taskName, boolean isSuccess, long startTime) {
        LogUtil.report(costTimeRuleNo, serviceName, taskName, startTime, isSuccess);
    }

    /**
     * 登录成功事件埋点
     */
    public void reportLoginSuccess(String event, String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        map.put("result", SUCCESS);
        MDC.put(CommonConstant.FLAG, "T");
        LogUtil.report(id, serviceName, map, userId);
    }


    /**
     * 成功上报
     *
     * @param event        事件
     * @param supplierId   卡片类型
     * @param pollingOrder pollingOrder
     */
    public void reportSuccessForLoan(String event, Integer supplierId, Map<String, Object> pollingOrder, String... param) {
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        if (supplierId != null) {
            map.put("supplierId", supplierId);
        }
        map.put("result", SUCCESS);
        MDC.put(CommonConstant.FLAG, "T");
        if (!Objects.isNull(pollingOrder)) {
            map.putAll(pollingOrder);
        }
        LogUtil.report(id, serviceName, map);
    }

    /**
     * 成功上报
     */
    public void reportSuccessRealTime(RealTimeReportInfo realTimeReportInfo) {
        if (realTimeReportInfo == null) {
            return;
        }
        try {
            Map<String, Object> map = new HashMap<>();
            Class<?> clazz = realTimeReportInfo.getClass();
            // 获取当前类及其所有父类的字段
            while (clazz != null) {
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    // 忽略静态字段
                    if (!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                        field.setAccessible(true); // 设置字段可访问
                        map.put(field.getName(), field.get(realTimeReportInfo)); // 将字段名和值放入 Map
                    }
                }// 过滤合成成员
                clazz = clazz.getSuperclass(); // 继续处理父类
            }
            map.put("result", SUCCESS);
            LogUtil.reportForRealTime(id, serviceName, map);
        } catch (Exception e) {
            LogUtil.runInfoLog("reportSuccessRealTime fail");
        }

    }

    /**
     * 失败上报
     *
     * @param event      事件
     * @param supplierId 卡片类型
     * @param pollingOrder pollingOrder
     */
    public void reportFailForLoan(String event, Integer supplierId, String traceId, String failCode, String failReason,
        Map<String, Object> pollingOrder, String... param) {
        Map<String, Object> map = new HashMap<>();
        map.put("event", event);
        if (supplierId != null) {
            map.put("supplierId", supplierId);
        }
        map.put("result", FAIL);
        MDC.put(CommonConstant.FLAG, "F");
        if (StringUtils.isNotBlank(traceId)) {
            map.put("traceId", traceId);
        }

        if (StringUtils.isNotBlank(MdcUtil.getSourceResponseCode())) {
            map.put("failCode", MdcUtil.getSourceResponseCode());
        } else if (StringUtils.isNotBlank(failCode)) {
            map.put("failCode", failCode);
        }

        if (StringUtils.isNotBlank(MdcUtil.getSourceResponseInfo())) {
            map.put("failReason", MdcUtil.getSourceResponseInfo());
        } else if (StringUtils.isNotBlank(failReason)) {
            map.put("failReason", failReason);
        }
        if (!Objects.isNull(pollingOrder)) {
            map.putAll(pollingOrder);
        }
        LogUtil.report(id, serviceName, map);
    }


    private FilterTest getFilterTest(String... param) {
        String userId = null;
        String deviceId = null;
        String deviceModel = null;
        // 交通卡回调通过RequestUtil获取不到,则通过param参数获取
        if (param != null && param.length == 3) {
            userId = param[0];
            deviceId = param[1];
            deviceModel = param[2];
        } else {
            userId = RequestUtils.getUid();
            deviceId = RequestUtils.getBaseHeader().getDeviceId();
            deviceModel = RequestUtils.getBaseHeader().getDeviceModel();
        }
        return filterTestLogs.checkTestUser(userId, deviceId, deviceModel);
    }

    /**
     * 获取银联控件的cplc
     *
     * @param baseRequest
     * @return
     */
    public String getReqCplc(BaseRequest baseRequest) {
        String cplc = baseRequest.getCplc();
        if (cplc == null || CommonConstant.DEFAULT_CPLC_NULL.equals(cplc)) {
            cplc = baseRequest.getTeeId();
        }
        return cplc;
    }
}

/*
 * Copyright (c) Honor Device Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.constant;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface SnsConstant {
	/**
	 * 短信模板需要的语言码
	 *
	 * @since 2023-08-24
	 */
	interface LanCode {
		/**
		 * 简体中文
		 */
		String ZH_CN = "zhCN";
	}

	/**
	 * 短信模板sp的类型
	 *
	 * @since 2023-08-24
	 */
	interface SpType {
		/**
		 * 岭南通
		 */
		String LNT = "lnt";

		/**
		 * 北京一卡通
		 */
		String BMAC = "bmac";

		/**
		 * 北京一卡通拥军卡
		 */
		String BMAC_YJ = "bmac_yj";

		/**
		 * 上海交通卡
		 */
		String SHANGHAI = "shanghai";

		/**
		 * 郑州电子发票回执
		 */
		String INVOICE = "invoice";

		/**
		 * 坏卡退卡
		 */
		String BAD_REFUND_SNS = "badRefundSns";

		/**
		 * 深圳坏卡退卡
		 */
		String SHENZHEN = "shenzhen";

		/**
		 * 重庆坏卡退卡模板
		 */
		String CHONGQING = "chongqing";

		/**
		 * 成都坏卡退卡模板
		 */
		String CDTFT = "chengdu";

		/**
		 * 短信验证
		 */
		String SNSCODE = "snsCode";

		/**
		 * 银行卡授权短信验证
		 */
		String AUTH_SNS_CODE = "authSnsCode";

		/**
		 * 销户
		 */
		String LOAN_LOGOFF = "logoff";

		/**
		 * 授信
		 */
		String CREDIT_APPLY = "creditApply";

		/**
		 * 交通卡自助退款申请
		 */
		String SELF_REFUND_APPLY = "selfRefundApply";
		/**
		 * 注销通知(已授信)
		 */
		String LOGOFF_NOTIFY_Credited = "logoffNotifyCredited";

		/**
		 * 注销通知(未授信)
		 */
		String LOGOFF_NOTIFY_Uncredited = "logoffNotifyUncredited";

		String MODIFY_PHONE = "modifyPhone";
	}
}

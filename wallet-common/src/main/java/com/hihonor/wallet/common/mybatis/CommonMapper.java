/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.mybatis;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface CommonMapper<T> extends BaseMapper<T> {
    /**
     * 批量插入
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int insertBatchSomeColumn(List<T> entityList);

    /**
     * 分表0~19批量插入（方法名最后的数字对应分表后缀）
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int insertBatch0(List<T> entityList);

    int insertBatch1(List<T> entityList);

    int insertBatch2(List<T> entityList);

    int insertBatch3(List<T> entityList);

    int insertBatch4(List<T> entityList);

    int insertBatch5(List<T> entityList);

    int insertBatch6(List<T> entityList);

    int insertBatch7(List<T> entityList);

    int insertBatch8(List<T> entityList);

    int insertBatch9(List<T> entityList);

    int insertBatch10(List<T> entityList);

    int insertBatch11(List<T> entityList);

    int insertBatch12(List<T> entityList);

    int insertBatch13(List<T> entityList);

    int insertBatch14(List<T> entityList);

    int insertBatch15(List<T> entityList);

    int insertBatch16(List<T> entityList);

    int insertBatch17(List<T> entityList);

    int insertBatch18(List<T> entityList);

    int insertBatch19(List<T> entityList);

    /**
     * 分表0~19分页查询（方法名最后的数字对应分表后缀）
     *
     * @param page Page
     * @param queryWrapper Wrapper
     * @return Page
     */
    <P extends IPage<T>> P selectPage0(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage1(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage2(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage3(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage4(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage5(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage6(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage7(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage8(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage9(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage10(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage11(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage12(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage13(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage14(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage15(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage16(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage17(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage18(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPage19(P page, @Param("ew") Wrapper<T> queryWrapper);

    /**
     * 通过ID批量更新（单次更新条数小于500行时效果比Update好）
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int updateBatchById(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    /**
     * 分表0~19批量更新（方法名最后的数字对应分表后缀）
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int updateBatchById0(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById1(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById2(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById3(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById4(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById5(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById6(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById7(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById8(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById9(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById10(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById11(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById12(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById13(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById14(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById15(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById16(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById17(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById18(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);

    int updateBatchById19(@Param("list") Collection<T> entityList, @Param("ew") Wrapper<T> updateWrapper);
}
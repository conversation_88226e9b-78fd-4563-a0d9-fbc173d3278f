/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.mybatis;

import com.hihonor.wallet.common.util.log.LogUtil;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public class SelectPageWithSubTableId extends AbstractMethod {
    private String subTableId;

    public SelectPageWithSubTableId(String subTableId) {
        super(SqlMethod.SELECT_PAGE.getMethod());
        this.subTableId = subTableId;
    }

    public SelectPageWithSubTableId(String name, String subTableId) {
        super(name);
        this.subTableId = subTableId;
    }

    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlMethod sqlMethod = SqlMethod.SELECT_PAGE;
        String sql = String.format(sqlMethod.getSql(), this.sqlFirst(), this.sqlSelectColumns(tableInfo, true),
            tableInfo.getTableName() + subTableId, this.sqlWhereEntityWrapper(true, tableInfo),
            this.sqlOrderBy(tableInfo), this.sqlComment());
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
        return this.addSelectMappedStatementForTable(mapperClass, this.getMethod(sqlMethod), sqlSource, tableInfo);
    }
}

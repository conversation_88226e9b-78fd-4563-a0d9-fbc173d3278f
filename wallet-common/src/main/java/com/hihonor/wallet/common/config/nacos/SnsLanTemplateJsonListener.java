/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.config.nacos;

import org.springframework.stereotype.Component;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Component
public class SnsLanTemplateJsonListener extends NacosJsonDefaultListener<SnsLanTemplateJson> {
    /**
     * SUPPORTED_APP
     */
    private static final String SNS_LAN_TEMPLATE = "loanSnsLanTemplate.json";


    @Override
    protected String getDataId() {
        return SNS_LAN_TEMPLATE;
    }

    @Override
    protected Class<? extends SnsLanTemplateJson> jsonToObjectClass() {
        return SnsLanTemplateJson.class;
    }
}

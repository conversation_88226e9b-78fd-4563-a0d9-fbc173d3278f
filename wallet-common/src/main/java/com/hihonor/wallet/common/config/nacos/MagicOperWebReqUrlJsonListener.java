/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.config.nacos;

import org.springframework.stereotype.Component;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Component
public class MagicOperWebReqUrlJsonListener extends NacosJsonDefaultListener<MagicOperWebReqUrlJson> {
    /**
     * SUPPORTED_APP
     */
    private static final String MAGICOPER_WEBREQURL = "loanMagicOperWebReqUrl.json";

    @Override
    protected String getDataId() {
        return MAGICOPER_WEBREQURL;
    }

    @Override
    protected Class<? extends MagicOperWebReqUrlJson> jsonToObjectClass() {
        return MagicOperWebReqUrlJson.class;
    }
}

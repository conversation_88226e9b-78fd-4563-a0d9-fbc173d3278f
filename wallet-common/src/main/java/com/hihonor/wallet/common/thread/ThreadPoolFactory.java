/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.thread;

import com.hihonor.wallet.common.config.nacos.ThreadPoolConfigJson;
import com.hihonor.wallet.common.config.nacos.ThreadPoolConfigJsonListener;

import com.alibaba.nacos.common.utils.CollectionUtils;

import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 线程池工厂
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Component
public class ThreadPoolFactory {
    private static final Map<String, TaskExecutor> map = new HashMap<>();

    @Resource
    ThreadPoolConfigJsonListener threadPoolConfigJsonListener;

    /**
     * 获取TaskExecutor
     *
     * @param name 配置名称
     * @return TaskExecutor
     */
    public TaskExecutor getTaskExecutor(String name) {
        TaskExecutor taskExecutor = map.get(name);
        if (taskExecutor == null) {
            synchronized (map) {
                taskExecutor = map.get(name);
                if (taskExecutor == null) {
                    taskExecutor = createTaskExecutor(name);
                    map.put(name, taskExecutor);
                }
            }
        }
        return taskExecutor;
    }

    /**
     * 根据配置，创建线程池
     *
     * @param name 配置名
     * @return TaskExecutor
     */
    private TaskExecutor createTaskExecutor(String name) {
        int corePoolSize = 5; // 线程池活跃的线程数
        int maxPoolSize = 10; // 线程池最大活跃的线程数
        int queueCapacity = 100; // 队列的最大容量

        ThreadPoolConfigJson.ThreadPoolConfig config = getThreadPoolConfig(name);
        if (config != null) {
            corePoolSize = config.getCorePoolSize();
            maxPoolSize = config.getMaxPoolSize();
            queueCapacity = config.getQueueCapacity();
        }
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setThreadGroupName(name);
        pool.setThreadNamePrefix(name);
        pool.setCorePoolSize(corePoolSize);
        pool.setMaxPoolSize(maxPoolSize);
        pool.setQueueCapacity(queueCapacity);
        pool.setWaitForTasksToCompleteOnShutdown(true);
        pool.initialize();
        return pool;
    }

    /**
     * 获取线程池配置
     *
     * @param name 配置名称
     * @return 线程池配置
     */
    public ThreadPoolConfigJson.ThreadPoolConfig getThreadPoolConfig(String name) {
        ThreadPoolConfigJson data = threadPoolConfigJsonListener.getData();
        List<ThreadPoolConfigJson.ThreadPoolConfig> configList = data.getThreadPoolConfigList();
        if (CollectionUtils.isNotEmpty(configList)) {
            for (ThreadPoolConfigJson.ThreadPoolConfig config : configList) {
                if (config.getName().equals(name)) {
                    return config;
                }
            }
        }
        return null;
    }
}

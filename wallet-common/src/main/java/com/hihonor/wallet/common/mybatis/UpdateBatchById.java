/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.common.mybatis;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.List;

import lombok.AllArgsConstructor;

/**
 * 通过ID批量更新，CASE WHEN单次更新条数在500条以内时效果由于UPDATE
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@AllArgsConstructor
public class UpdateBatchById extends AbstractMethod {
    private MySqlMethod mySqlMethod;

    private String tableIndex;

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(mySqlMethod.getSql(), tableInfo.getTableName() + tableIndex, this.sqlSet(tableInfo),
            tableInfo.getKeyColumn(), this.sqlIn(tableInfo.getKeyProperty()));
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, mySqlMethod.getMethod(), sqlSource);
    }

    private String sqlSet(TableInfo tableInfo) {
        List<TableFieldInfo> fieldList = tableInfo.getFieldList();
        StringBuilder sb = new StringBuilder();
        for (TableFieldInfo fieldInfo : fieldList) {
            sb.append("<if test=\"ew.updateFields.contains(&quot;")
                .append(fieldInfo.getColumn())
                .append("&quot;)\">")
                .append(fieldInfo.getColumn())
                .append(" =\n")
                .append("CASE ")
                .append(tableInfo.getKeyColumn())
                .append("\n")
                .append("<foreach collection=\"list\" item=\"et\" >\n")
                .append("WHEN #{et.")
                .append(tableInfo.getKeyProperty())
                .append("} THEN #{et.")
                .append(fieldInfo.getProperty())
                .append("}\n")
                .append("</foreach>\n")
                .append("END ,\n")
                .append("</if>\n");
        }
        return "<set>\n" + sb + "</set>";
    }

    private String sqlIn(String keyProperty) {
        return "<foreach collection=\"list\" item=\"et\" separator=\",\" open=\"(\" close=\")\">\n" + "#{et."
            + keyProperty + "}" + "</foreach>\n";
    }
}
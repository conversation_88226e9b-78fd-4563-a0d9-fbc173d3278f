/*
 * Copyright (c) Honor Device Co., Ltd. 2021-2022. All rights reserved.
 */

package com.hihonor.wallet.common.util.http;

import com.hihonor.wallet.common.util.log.SensitiveLog;

import org.junit.Test;
import org.springframework.stereotype.Component;


/**
 * RestTemplate工具类
 *
 * <AUTHOR> y00020640
 * @since 2022-06-06
 */
@Component
public class HttpTemplateTest {



    @Test
    public void testhideMarkLog01() {
        String s= "{\"code\":0,\n" +
                "\"desc\":\"success\",\n" +
                "\"success\":true,\n" +
                "\"timestamp\":\"1741502026737\",\n" +
                "\"data\":\"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\",\n" +
                "sign\":\"dZ9MY******p6isFfa******************/5q6/2OrbZm6m6bqHI62**********************************************************7FuM8/fjOIGe7UfwDEIAXptpFCRMV+5CliRl1v5irfrlRuHcg2FJBBM1s=\"}";
        String s1 = SensitiveLog.hideMarkLog(s);
        System.out.println(s1);
    }

    @Test
    public void testhideMarkLog02() {
        String s= "";
        String s1 = SensitiveLog.hideMarkLog(s);
        String[] splits = s1.split(",");
        for (String split : splits) {
            System.out.println(split);
        }
    }

    @Test
    public void testhideMarkLog03() {
        String s = "{\"shouldRepayAmount\":101***88,\"shouldRepayPrinAmount\":100***00,\"shouldRepayInterAmount\":19***8,\"shouldRepayServiceFee\":*,\"firstRepayDate\":\"20250409\",\"lastRepayDate\":\"20260309\",\"reductionAmount\":*,\"stakeholders\":\"重庆两江新区盛际小额贷款有限公司&重庆两江新区盛际小额贷款有限公司\",\"annualRate\":\"3.**24\",\"repayPlanTerms\":[{\"termNo\":1,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***1,\"termPrincipal\":83***7,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***1,\"termServiceFee\":*},{\"termNo\":2,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":3,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":4,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":5,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":6,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":7,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":8,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":9,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":10,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":11,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*},{\"termNo\":12,\"shouldRepayDate\":\"20****09\",\"termStatus\":0,\"termAmount\":84***7,\"termPrincipal\":83***3,\"termInterest\":16**4,\"termReductionAmount\":*,\"amount\":84***7,\"termServiceFee\":*}]}";
        String s1 = SensitiveLog.hideMarkLog(s);
        String[] splits = s1.split(",");
        for (String split : splits) {
            System.out.println(split);
        }
    }


    @Test
    public void testhideMarkLog04() {
        String s = "{\"applyNo\":\"2***095********1280910\",\"applyStatus\":6,\"refuseMsg\":\"\",\"refuseControlDays\":0,\"verifyList\":[\"AGREEMENT_COMMON\"]}}";
        String s1 = SensitiveLog.hideMarkLog(s);
        String[] splits = s1.split(",");
        for (String split : splits) {
            System.out.println(split);
        }
    }

    @Test
    public void testhideMarkLog05() {
        String s = " {\"encryptedParams\":\"25c7e4dd8f4e33c316074258ec1ddff1:683befe999a8a7423360078cc38af2bcd8613b60c1ae3249850882ba2e71fa5c4af62f01a4ff4e4389d1e105193978b50aeaec9251ae443e9f7e3d7bd9db03beddc4f5e77ddc89118a972c3c89c6275a38b9ccbcad69c4df11a13545ac559b5b309367709a2b3ce6fadbdc334a702fddda7dca7466b044fd0e4888258cada4718dcf4e7b021a0f9dc919345af6bb36e3e1d89bc55a05dabbea03821d2c094e42b2cda57287801172005b72c8467a2c09c5f01ed2be93dcd12ddc901c64789675c123720d87d01d\",\"country\":\"CN\",\"language\":\"zh-CN\",\"de****Id\":\"6600BFC8DE13F3E5********************************4580CB80EB4233F6\",\"deviceOsVersion\":\"13\",\"clientPlatform\":1,\"cplc\":\"NONNFC\",\"deviceType\":1}";
        String s1 = SensitiveLog.hideMarkLog(s);
        String[] splits = s1.split(",");
        for (String split : splits) {
            System.out.println(split);
        }
    }

    @Test
    public void testhideMarkLog06() {
        String s = "{\"openId\":\"1zNrFHXGLqITVFOxNKCb5cB6JLd1c00Y\",\"couponId\":\"390732*******390242\",\"couponBatchNo\":\"1507954766\",\"userId\":\"751728******8861265\"}";
        String s1 = SensitiveLog.hideMarkLog(s);
        String[] splits = s1.split(",");
        for (String split : splits) {
            System.out.println(split);
        }
    }






    @Test
    public void testhideMarkStr() {
        String s = SensitiveLog.hideMarkStr("1111111111111111111111111111111");
        System.out.println(s);
    }

}

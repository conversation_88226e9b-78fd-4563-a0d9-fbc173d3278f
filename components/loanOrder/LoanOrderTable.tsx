import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Button,
  Modal,
  useDisclosure,
  ModalContent,
  Input,
  Select,
  SelectItem,
  DatePicker,
  NumberInput, Checkbox,
} from "@heroui/react";
import { ModalBody, ModalFooter, ModalHeader } from "@heroui/modal";

import { DeleteIcon, EditIcon } from "../icons";
import {LoanOrder, loanStatuses, repayMethods, repayTypes} from "@/types";

const columns = [
  { name: "内部订单号", uid: "applyNo" },
  { name: "外部订单号", uid: "outOrderNo" },
  { name: "借款状态", uid: "status" },
  { name: "借款时间", uid: "applyDate" },
  { name: "放款日期", uid: "paydate" },
  { name: "借款金额", uid: "loanAmount" },
  { name: "借款期数", uid: "totalTerm" },
  { name: "还款方式", uid: "repayMethod" },
  { name: "当前期数", uid: "currentTerm" },
  { name: "应还金额", uid: "dueRepay" },
  { name: "支持的还款方式", uid: "supportRepayTypeDetails" },
  { name: "操作", uid: "actions" },
];

export default function LoanOrderTable(props: {
  className: string;
  loanOrders?: LoanOrder[];
  setLoanOrders: (loanOrders: LoanOrder[]) => void;
}) {
  const [newLoanOrder, setNewLoanOrder] = useState<LoanOrder>({});
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const createProductInfo = () => {
    props.setLoanOrders([...(props.loanOrders || []), newLoanOrder]);
    onClose();
  };

  function deleteLoanOrder(applyNo: number) {
    props.setLoanOrders(
      props.loanOrders.filter((item) => item.applyNo !== applyNo),
    );
  }

  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "status":
        return (
          <div>
            {loanStatuses.find((status) => status.key === cellValue)?.label}
          </div>
        );
      case "repayMethod":
        return (
          <div>
            {repayMethods.find((method) => method.key === cellValue)?.label}
          </div>
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除">
              <span className="text-lg text-danger cursor-pointer active:opacity-50" onClick={() => deleteLoanOrder(item.applyNo)}>
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );

        case "supportRepayTypeDetails":
          console.log("==========", cellValue)
          const repayTypeLabelMap = new Map(
            repayTypes.map(item => [item.key, item.label])
          );
          if (!cellValue || cellValue.length === 0) {
            return '';
          }
          return cellValue.map(item => repayTypeLabelMap.get(item.type)).join(',');
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>借款订单</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table className={props.className} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.loanOrders}>
          {(item) => (
            <TableRow key={item.outOrderNo}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的订单
              </ModalHeader>
              <ModalBody>
                <Input
                  label="内部订单号"
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      applyNo: value,
                    });
                  }}
                />
                <Input
                  label="外部订单号"
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      outOrderNo: value,
                    });
                  }}
                />
                <Select
                  className="max-w-xs"
                  label="借款状态"
                  onSelectionChange={(key) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      status: parseInt(key.anchorKey as string),
                    });
                  }}
                >
                  {loanStatuses.map((status) => (
                    <SelectItem key={status.key}>{status.label}</SelectItem>
                  ))}
                </Select>

                <DatePicker
                  label="借款时间"
                  onChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      applyDate: value?.toString().replace(/-/g, "") || "",
                    });
                  }}
                />

                <DatePicker
                  label="放款日期"
                  onChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      paydate: value?.toString().replace(/-/g, "") || "",
                    });
                  }}
                />
                <NumberInput
                  className="max-w-xs"
                  label="借款金额"
                  minValue={10000}
                  step={10000}
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      loanAmount: value,
                    });
                  }}
                />

                <NumberInput
                  className="max-w-xs"
                  label="借款期数"
                  maxValue={12}
                  minValue={3}
                  step={1}
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      totalTerm: value,
                    });
                  }}
                />

                <Select
                  className="max-w-xs"
                  label="还款方式"
                  onSelectionChange={(key) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      repayMethod: parseInt(key.anchorKey as string),
                    });
                  }}
                >
                  {repayMethods.map((status) => (
                    <SelectItem key={status.key}>{status.label}</SelectItem>
                  ))}
                </Select>

                <NumberInput
                  className="max-w-xs"
                  label="应还金额"
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      dueRepay: value,
                    });
                  }}
                />

                <NumberInput
                  className="max-w-xs"
                  label="当前期数"
                  onValueChange={(value) => {
                    setNewLoanOrder({
                      ...newLoanOrder,
                      currentTerm: value,
                    });
                  }}
                />

                <div>
                  <Select
                    className="max-w-xs"
                    label="支持的还款方式"
                    selectionMode="multiple"
                    onSelectionChange={(keys) => {
                      setNewLoanOrder(
                        {
                          ...newLoanOrder,
                          supportRepayTypeDetails: [...keys].map((key) => {
                            return {
                              type: parseInt(key),
                              disable: false
                            };
                          }),
                        },
                      )
                    }}
                  >
                    {repayTypes.map((repayType) => (
                      <SelectItem key={repayType.key}>{repayType.label}</SelectItem>
                    ))}
                  </Select>
                </div>

              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createProductInfo}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
